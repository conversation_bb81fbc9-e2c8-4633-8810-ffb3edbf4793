import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject } from 'rxjs';

export type Theme = 'light' | 'dark' | 'auto';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'qts-theme';
  private currentTheme = new BehaviorSubject<Theme>('light');
  private isDarkMode = new BehaviorSubject<boolean>(false);
  
  public theme$ = this.currentTheme.asObservable();
  public isDarkMode$ = this.isDarkMode.asObservable();
  
  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      this.initializeTheme();
      this.setupMediaQueryListener();
    }
  }
  
  private initializeTheme(): void {
    const savedTheme = localStorage.getItem(this.THEME_KEY) as Theme;
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme) {
      this.setTheme(savedTheme);
    } else if (prefersDark) {
      this.setTheme('dark');
    } else {
      this.setTheme('light');
    }
  }
  
  private setupMediaQueryListener(): void {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', (e) => {
      if (this.currentTheme.value === 'auto') {
        this.applyTheme(e.matches ? 'dark' : 'light');
      }
    });
  }
  
  setTheme(theme: Theme): void {
    this.currentTheme.next(theme);
    
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem(this.THEME_KEY, theme);
      
      if (theme === 'auto') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        this.applyTheme(prefersDark ? 'dark' : 'light');
      } else {
        this.applyTheme(theme);
      }
    }
  }
  
  private applyTheme(theme: 'light' | 'dark'): void {
    if (isPlatformBrowser(this.platformId)) {
      const root = document.documentElement;
      
      if (theme === 'dark') {
        root.setAttribute('data-theme', 'dark');
        root.classList.add('dark-theme');
        this.isDarkMode.next(true);
      } else {
        root.setAttribute('data-theme', 'light');
        root.classList.remove('dark-theme');
        this.isDarkMode.next(false);
      }
      
      // Dispatch custom event for components that need to react to theme changes
      window.dispatchEvent(new CustomEvent('theme-changed', { 
        detail: { theme, isDark: theme === 'dark' } 
      }));
    }
  }
  
  toggleTheme(): void {
    const current = this.currentTheme.value;
    if (current === 'light') {
      this.setTheme('dark');
    } else if (current === 'dark') {
      this.setTheme('auto');
    } else {
      this.setTheme('light');
    }
  }
  
  getCurrentTheme(): Theme {
    return this.currentTheme.value;
  }
  
  getIsDarkMode(): boolean {
    return this.isDarkMode.value;
  }
}
