import { Component, OnInit, OnD<PERSON>roy, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { HeroAnimationComponent } from '../../components/hero-animation/hero-animation.component';
import { TestimonialsComponent } from '../../components/testimonials/testimonials.component';
import { ScrollAnimationService } from '../../services/scroll-animation.service';
import { SEOService } from '../../services/seo.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatCardModule, MatIconModule, RouterLink, HeroAnimationComponent, TestimonialsComponent],
  template: `
    <!-- Enhanced Hero Section -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="hero-particles"></div>
        <div class="hero-gradient-overlay"></div>
      </div>
      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-badge">
            <mat-icon>verified</mat-icon>
            <span>Trusted by Fortune 500 Companies</span>
          </div>
          <h1 class="hero-title">
            <span class="title-line">Transform Your Business</span>
            <span class="title-line gradient-text">With Enterprise Technology</span>
          </h1>
          <p class="hero-subtitle">
            Quadrate Tech Solutions delivers cutting-edge software development, AI-powered automation,
            and cloud infrastructure that accelerates growth for modern enterprises across the globe.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>business</mat-icon>
              </div>
              <div class="stat-content">
                <span class="stat-number">500+</span>
                <span class="stat-label">Enterprise Clients</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>public</mat-icon>
              </div>
              <div class="stat-content">
                <span class="stat-number">25+</span>
                <span class="stat-label">Countries Served</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>trending_up</mat-icon>
              </div>
              <div class="stat-content">
                <span class="stat-number">99.9%</span>
                <span class="stat-label">Success Rate</span>
              </div>
            </div>
          </div>
          <div class="hero-actions">
            <button mat-raised-button color="primary" routerLink="/solutions" class="primary-cta">
              <mat-icon>rocket_launch</mat-icon>
              Start Your Transformation
            </button>
            <button mat-stroked-button routerLink="/support" class="secondary-cta">
              <mat-icon>schedule</mat-icon>
              Schedule Consultation
            </button>
          </div>
          <div class="hero-trust-indicators">
            <span>Trusted by:</span>
            <div class="trust-logos">
              <div class="trust-logo">Microsoft Partner</div>
              <div class="trust-logo">AWS Advanced</div>
              <div class="trust-logo">Google Cloud</div>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div class="hero-image-container">
            <app-hero-animation></app-hero-animation>
            <div class="floating-cards">
              <div class="floating-card card-1">
                <mat-icon>cloud_queue</mat-icon>
                <span>Cloud Solutions</span>
              </div>
              <div class="floating-card card-2">
                <mat-icon>psychology</mat-icon>
                <span>AI & ML</span>
              </div>
              <div class="floating-card card-3">
                <mat-icon>build_circle</mat-icon>
                <span>DevOps</span>
              </div>
              <div class="floating-card card-4">
                <mat-icon>security</mat-icon>
                <span>Enterprise Security</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Why Choose Us Section -->
    <div class="why-choose-us">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Why Global Enterprises Choose Quadrate</h2>
          <p class="section-subtitle">We combine deep technical expertise with proven business acumen to deliver transformational results</p>
        </div>
        <div class="benefits-grid">
          <div class="benefit-card">
            <div class="benefit-icon">
              <mat-icon>speed</mat-icon>
            </div>
            <h3>Rapid Implementation</h3>
            <p>Deploy enterprise solutions 3x faster with our proven methodologies and pre-built frameworks</p>
            <div class="benefit-metric">
              <span class="metric-number">70%</span>
              <span class="metric-label">Faster Time-to-Market</span>
            </div>
          </div>
          <div class="benefit-card">
            <div class="benefit-icon">
              <mat-icon>security</mat-icon>
            </div>
            <h3>Enterprise Security</h3>
            <p>Bank-grade security with SOC 2, ISO 27001 compliance and zero-trust architecture</p>
            <div class="benefit-metric">
              <span class="metric-number">100%</span>
              <span class="metric-label">Security Compliance</span>
            </div>
          </div>
          <div class="benefit-card">
            <div class="benefit-icon">
              <mat-icon>support_agent</mat-icon>
            </div>
            <h3>24/7 Global Support</h3>
            <p>Round-the-clock support from our global team of certified engineers and solution architects</p>
            <div class="benefit-metric">
              <span class="metric-number">&lt;15min</span>
              <span class="metric-label">Response Time</span>
            </div>
          </div>
          <div class="benefit-card">
            <div class="benefit-icon">
              <mat-icon>trending_up</mat-icon>
            </div>
            <h3>Proven ROI</h3>
            <p>Average 300% ROI within 12 months through operational efficiency and cost optimization</p>
            <div class="benefit-metric">
              <span class="metric-number">300%</span>
              <span class="metric-label">Average ROI</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Solutions Overview -->
    <div class="solutions-overview" #solutionsSection>
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Enterprise Solutions That Scale</h2>
          <p class="section-subtitle">Comprehensive technology solutions designed for enterprise performance and growth</p>
        </div>
        <div class="solutions-grid">
          @for (solution of enterpriseSolutions; track solution.title) {
            <div class="solution-card" [attr.data-aos]="'fade-up'" [attr.data-aos-delay]="$index * 100">
              <div class="solution-header">
                <div class="solution-icon" [style.background]="solution.gradient">
                  <mat-icon>{{ solution.icon }}</mat-icon>
                </div>
                <div class="solution-badge">{{ solution.category }}</div>
              </div>
              <div class="solution-content">
                <h3>{{ solution.title }}</h3>
                <p>{{ solution.description }}</p>
                <ul class="solution-features">
                  @for (feature of solution.features; track feature) {
                    <li>
                      <mat-icon>check_circle</mat-icon>
                      <span>{{ feature }}</span>
                    </li>
                  }
                </ul>
              </div>
              <div class="solution-footer">
                <button mat-raised-button color="primary" routerLink="/solutions">
                  Explore Solution
                  <mat-icon>arrow_forward</mat-icon>
                </button>
              </div>
            </div>
          }
        </div>
      </div>
    </div>

    <!-- Industry Focus -->
    <div class="industry-focus">
      <div class="container">
        <h2>Trusted Across Industries</h2>
        <p class="section-subtitle">Delivering specialized solutions for diverse industry requirements</p>
        <div class="industry-grid">
          @for (industry of industries; track industry.name) {
            <div class="industry-card" routerLink="/industries">
              <mat-icon [style.color]="industry.color">{{ industry.icon }}</mat-icon>
              <h4>{{ industry.name }}</h4>
              <p>{{ industry.description }}</p>
            </div>
          }
        </div>
      </div>
    </div>

    <!-- Customer Success -->
    <app-testimonials></app-testimonials>

    <!-- Technology Partnerships -->
    <div class="partnerships-section">
      <div class="container">
        <h2>Strategic Technology Partnerships</h2>
        <p class="partnerships-subtitle">Certified partnerships with leading technology providers</p>
        <div class="partnerships-grid">
          <div class="partnership-item">
            <div class="partnership-icon microsoft">
              <img src="https://via.placeholder.com/80x80/0078D4/FFFFFF?text=MS" alt="Microsoft">
            </div>
            <h3>Microsoft Gold Partner</h3>
            <p>Azure, Office 365, and enterprise solutions</p>
            <div class="partnership-badge">Gold Certified</div>
          </div>
          <div class="partnership-item">
            <div class="partnership-icon aws">
              <img src="https://via.placeholder.com/80x80/FF9900/FFFFFF?text=AWS" alt="AWS">
            </div>
            <h3>AWS Advanced Partner</h3>
            <p>Cloud infrastructure and migration services</p>
            <div class="partnership-badge">Advanced Tier</div>
          </div>
          <div class="partnership-item">
            <div class="partnership-icon google">
              <img src="https://via.placeholder.com/80x80/4285F4/FFFFFF?text=GCP" alt="Google Cloud">
            </div>
            <h3>Google Cloud Partner</h3>
            <p>AI/ML and data analytics solutions</p>
            <div class="partnership-badge">Certified</div>
          </div>
          <div class="partnership-item">
            <div class="partnership-icon salesforce">
              <img src="https://via.placeholder.com/80x80/00A1E0/FFFFFF?text=SF" alt="Salesforce">
            </div>
            <h3>Salesforce Partner</h3>
            <p>CRM and customer experience platforms</p>
            <div class="partnership-badge">Certified</div>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="cta-section">
      <div class="container">
        <h2>Ready to Transform Your Enterprise?</h2>
        <p>Join hundreds of organizations that trust Quadrate Technologies to drive their digital transformation.</p>
        <div class="cta-actions">
          <button mat-raised-button color="primary" routerLink="/support">Get Started Today</button>
          <button mat-stroked-button routerLink="/case-studies">View Success Stories</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .hero-section {
      background: var(--primary-gradient);
      color: white;
      padding: 6rem 0;
      min-height: 90vh;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
    }

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }

    .hero-particles {
      position: absolute;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 20% 80%, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
      animation: float 20s ease-in-out infinite;
    }

    .hero-gradient-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(6, 7, 225, 0.9) 0%, rgba(77, 10, 255, 0.8) 100%);
    }

    .hero-container {
      max-width: 1400px;
      width: 100%;
      margin: 0 auto;
      padding: 0 2rem;
      display: grid;
      grid-template-columns: 1.2fr 1fr;
      gap: 4rem;
      align-items: center;
      position: relative;
      z-index: 2;
      box-sizing: border-box;
    }

    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 0.5rem 1rem;
      border-radius: 50px;
      font-size: 0.9rem;
      margin-bottom: 1.5rem;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .hero-title {
      font-size: 4rem;
      font-weight: 800;
      margin-bottom: 1.5rem;
      line-height: 1.1;
    }

    .title-line {
      display: block;
    }

    .gradient-text {
      background: linear-gradient(135deg, #06B6D4 0%, #10B981 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .hero-subtitle {
      font-size: 1.3rem;
      margin-bottom: 2.5rem;
      opacity: 0.95;
      line-height: 1.6;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .hero-stats {
      display: flex;
      gap: 2rem;
      margin: 3rem 0;
      flex-wrap: wrap;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 1rem 1.5rem;
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      flex: 1;
      min-width: 200px;
    }

    .stat-icon {
      background: var(--secondary-gradient);
      padding: 0.5rem;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .stat-content {
      display: flex;
      flex-direction: column;
    }

    .stat-number {
      font-size: 1.8rem;
      font-weight: 700;
      color: white;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
      margin-top: 0.2rem;
    }

    .hero-actions {
      display: flex;
      gap: 1.5rem;
      margin-bottom: 2rem;
      flex-wrap: wrap;
    }

    .primary-cta, .secondary-cta {
      padding: 1rem 2rem;
      font-size: 1.1rem;
      font-weight: 600;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      min-width: 200px;
      justify-content: center;
    }

    .primary-cta {
      background: var(--secondary-gradient);
      border: none;
      color: white;
      box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
    }

    .primary-cta:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
    }

    .secondary-cta {
      background: transparent;
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
    }

    .secondary-cta:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .hero-trust-indicators {
      display: flex;
      align-items: center;
      gap: 1rem;
      font-size: 0.9rem;
      opacity: 0.8;
      flex-wrap: wrap;
    }

    .trust-logos {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .trust-logo {
      background: rgba(255, 255, 255, 0.1);
      padding: 0.3rem 0.8rem;
      border-radius: 20px;
      font-size: 0.8rem;
    }

    .hero-visual {
      position: relative;
      z-index: 1;
    }

    .hero-image-container {
      position: relative;
    }

    .floating-cards {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
    }

    .floating-card {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 1rem;
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
      animation: float 6s ease-in-out infinite;
    }

    .floating-card.card-1 {
      top: 15%;
      right: 10%;
      animation-delay: 0s;
    }

    .floating-card.card-2 {
      top: 65%;
      right: 15%;
      animation-delay: 2s;
    }

    .floating-card.card-3 {
      top: 35%;
      left: 5%;
      animation-delay: 4s;
    }

    .floating-card.card-4 {
      top: 75%;
      left: 15%;
      animation-delay: 6s;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    .container {
      max-width: 1400px;
      width: 100%;
      margin: 0 auto;
      padding: 0 2rem;
      box-sizing: border-box;
    }

    .section-header {
      text-align: center;
      margin-bottom: 4rem;
    }

    .section-title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }

    /* Why Choose Us Section */
    .why-choose-us {
      padding: 8rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .benefits-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .benefit-card {
      background: white;
      padding: 2.5rem;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .benefit-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      border-color: var(--primary-blue);
    }

    .benefit-icon {
      background: var(--primary-gradient);
      width: 80px;
      height: 80px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1.5rem;
      color: white;
      font-size: 2rem;
    }

    .benefit-card h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .benefit-card p {
      color: var(--text-primary);
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .benefit-metric {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
    }

    .metric-number {
      font-size: 2rem;
      font-weight: 700;
      color: var(--primary-blue);
    }

    .metric-label {
      font-size: 0.9rem;
      color: var(--text-primary);
      font-weight: 500;
    }

    .solutions-overview {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f0f0ff 100%);
      position: relative;
      overflow: hidden;
    }

    .solutions-overview::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    .section-title {
      text-align: center;
      margin-bottom: 1rem;
      font-size: 3rem;
      font-weight: 800;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      position: relative;
      z-index: 2;
    }

    .section-subtitle {
      text-align: center;
      margin-bottom: 4rem;
      font-size: 1.2rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      position: relative;
      z-index: 2;
    }

    .solutions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2.5rem;
      position: relative;
      z-index: 2;
    }

    .solution-card {
      background: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-top: 4px solid transparent;
    }

    .solution-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    }

    .solution-icon {
      font-size: 3rem;
      margin-bottom: 1.5rem;
    }

    .solution-card h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .solution-card p {
      color: var(--text-primary);
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }

    .solution-features {
      list-style: none;
      padding: 0;
      margin: 0 0 2rem 0;
    }

    .solution-features li {
      padding: 0.5rem 0;
      position: relative;
      padding-left: 1.5rem;
      color: var(--text-primary);
    }

    .solution-features li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: var(--nlp-green);
      font-weight: bold;
    }

    .solution-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
    }

    .solution-badge {
      background: var(--primary-gradient);
      color: white;
      padding: 0.3rem 0.8rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .solution-content {
      margin-bottom: 1.5rem;
    }

    .solution-footer button {
      width: 100%;
      padding: 1rem;
      border-radius: 12px;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .industry-focus {
      padding: 6rem 0;
      background: #f8f9fa;
    }

    .industry-focus h2 {
      text-align: center;
      margin-bottom: 1rem;
      font-size: 2.5rem;
      font-weight: 800;
      color: var(--text-primary);
    }

    .industry-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .industry-card {
      background: white;
      padding: 2rem;
      border-radius: 12px;
      text-align: center;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .industry-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .industry-card mat-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .industry-card h4 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--text-primary);
    }

    .industry-card p {
      color: var(--text-primary);
      font-size: 0.9rem;
      line-height: 1.5;
    }

    .partnerships-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .partnerships-section h2 {
      text-align: center;
      margin-bottom: 1rem;
      font-size: 2.5rem;
      font-weight: 800;
      color: var(--text-primary);
    }

    .partnerships-subtitle {
      text-align: center;
      margin-bottom: 4rem;
      font-size: 1.1rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .partnerships-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2.5rem;
    }

    .partnership-item {
      text-align: center;
      padding: 3rem 2rem;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .partnership-item:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      border-color: #0607E1;
    }

    .partnership-icon {
      margin-bottom: 1.5rem;
    }

    .partnership-icon img {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .partnership-item h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      font-size: 1.3rem;
      font-weight: 700;
    }

    .partnership-item p {
      color: var(--text-secondary);
      line-height: 1.6;
      margin-bottom: 1rem;
    }

    .partnership-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .cta-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2.5rem;
      opacity: 0.9;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-actions {
      display: flex;
      gap: 1.5rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .hero-section {
        min-height: 80vh;
        padding: 4rem 0;
      }

      .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
      }

      .hero-title {
        font-size: 2.8rem;
      }

      .hero-subtitle {
        font-size: 1.2rem;
      }

      .hero-stats {
        flex-direction: column;
        gap: 1rem;
      }

      .stat-item {
        min-width: auto;
        width: 100%;
      }

      .hero-actions {
        flex-direction: column;
        gap: 1rem;
      }

      .primary-cta, .secondary-cta {
        width: 100%;
        min-width: auto;
      }

      .hero-trust-indicators {
        justify-content: center;
      }

      .floating-cards {
        display: none;
      }

      .section-title {
        font-size: 2.2rem;
      }

      .section-subtitle {
        font-size: 1.1rem;
      }

      .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .benefit-card {
        padding: 2rem;
      }

      .solutions-grid {
        grid-template-columns: 1fr;
      }

      .industry-grid {
        grid-template-columns: 1fr;
      }

      .partnerships-grid {
        grid-template-columns: 1fr;
      }

      .cta-section h2 {
        font-size: 2rem;
      }

      .cta-actions {
        flex-direction: column;
        align-items: center;
      }

      .partnerships-grid {
        grid-template-columns: 1fr;
      }

      .container {
        padding: 0 1rem;
      }

      .why-choose-us {
        padding: 4rem 0;
      }

      .solutions-overview {
        padding: 4rem 0;
      }
    }

    @media (max-width: 480px) {
      .hero-title {
        font-size: 2.2rem;
      }

      .hero-subtitle {
        font-size: 1rem;
      }

      .section-title {
        font-size: 1.8rem;
      }

      .benefit-card, .solution-card {
        padding: 1.5rem;
      }

      .stat-item {
        padding: 0.8rem 1rem;
      }

      .hero-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }
  `]
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('servicesSection') servicesSection!: ElementRef;

  enterpriseSolutions = [
    {
      title: 'Cloud Migration & Modernization',
      description: 'Accelerate your digital transformation with comprehensive cloud migration strategies and modern architecture design.',
      features: ['Azure/AWS Migration', 'Microservices Architecture', 'DevOps Implementation', 'Performance Optimization'],
      icon: 'cloud_upload',
      color: '#0078D4',
      gradient: 'linear-gradient(135deg, #0078D4 0%, #00BCF2 100%)',
      category: 'Infrastructure'
    },
    {
      title: 'Enterprise Software Development',
      description: 'Build scalable, secure enterprise applications that drive business growth and operational excellence.',
      features: ['Custom Enterprise Apps', 'API Development', 'System Integration', 'Legacy Modernization'],
      icon: 'business',
      color: '#0607E1',
      gradient: 'linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%)',
      category: 'Development'
    },
    {
      title: 'AI & Data Analytics',
      description: 'Unlock the power of your data with advanced analytics, machine learning, and artificial intelligence solutions.',
      features: ['Predictive Analytics', 'Machine Learning Models', 'Business Intelligence', 'Data Visualization'],
      icon: 'psychology',
      color: '#4D0AFF',
      gradient: 'linear-gradient(135deg, #4D0AFF 0%, #8B5CF6 100%)',
      category: 'AI & Analytics'
    },
    {
      title: 'Cybersecurity Solutions',
      description: 'Protect your enterprise with comprehensive security frameworks, threat detection, and compliance management.',
      features: ['Security Assessment', 'Threat Detection', 'Compliance Management', 'Identity Management'],
      icon: 'security',
      color: '#EF4444',
      gradient: 'linear-gradient(135deg, #EF4444 0%, #F59E0B 100%)',
      category: 'Security'
    }
  ];

  industries = [
    {
      name: 'Healthcare',
      description: 'HIPAA-compliant solutions for patient care and medical data management',
      icon: 'local_hospital',
      color: '#e91e63'
    },
    {
      name: 'Financial Services',
      description: 'Secure, compliant solutions for banking and financial institutions',
      icon: 'account_balance',
      color: '#2196f3'
    },
    {
      name: 'Manufacturing',
      description: 'Industry 4.0 solutions for smart manufacturing and supply chain',
      icon: 'precision_manufacturing',
      color: '#ff9800'
    },
    {
      name: 'Retail & E-commerce',
      description: 'Omnichannel solutions for modern retail experiences',
      icon: 'shopping_cart',
      color: '#4caf50'
    }
  ];

  constructor(
    private scrollAnimationService: ScrollAnimationService,
    private seoService: SEOService
  ) {}

  ngOnInit() {
    // Update SEO for home page
    this.seoService.updateSEO({
      title: 'Enterprise Technology Solutions - Quadrate Technologies',
      description: 'Leading enterprise technology transformation through innovative solutions. Cloud migration, AI implementation, and digital transformation services.',
      keywords: 'enterprise technology, digital transformation, cloud migration, AI solutions, software development'
    });
  }

  ngAfterViewInit() {
    // Observe sections for scroll animations
    if (this.servicesSection) {
      this.scrollAnimationService.observeElement(this.servicesSection);
    }
  }

  ngOnDestroy() {
    // Clean up observers
    if (this.servicesSection) {
      this.scrollAnimationService.unobserveElement(this.servicesSection);
    }
  }
}
