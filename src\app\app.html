<!-- App Container -->
<div class="app-container">
  <!-- Navigation Header -->
  <mat-toolbar color="primary" class="navbar">
  <div class="navbar-container">
    <!-- Logo -->
    <div class="navbar-brand">
      <a routerLink="/home" class="brand-link">
        <img src="https://ik.imagekit.io/quadrate/QTS%20Logo%20Primary.png"
             alt="Quadrate Tech Solutions"
             class="logo">
        <span class="brand-text">{{ title() }}</span>
      </a>
    </div>

    <!-- Desktop Navigation -->
    <nav class="navbar-nav desktop-nav">
      @for (item of navigationItems; track item.path) {
        <a mat-button
           [routerLink]="item.path"
           routerLinkActive="active"
           class="nav-link">
          {{ item.label }}
        </a>
      }

      <!-- Theme Toggle -->
      <app-theme-toggle></app-theme-toggle>
    </nav>

    <!-- Mobile Menu Button -->
    <button mat-icon-button
            class="mobile-menu-btn"
            [matMenuTriggerFor]="mobileMenu">
      <mat-icon>menu</mat-icon>
    </button>

    <!-- Mobile Menu -->
    <mat-menu #mobileMenu="matMenu" class="mobile-menu">
      @for (item of navigationItems; track item.path) {
        <a mat-menu-item
           [routerLink]="item.path"
           routerLinkActive="active">
          {{ item.label }}
        </a>
      }
    </mat-menu>
  </div>
</mat-toolbar>

<!-- Main Content -->
<main class="main-content">
  <router-outlet />
</main>

<!-- Footer -->
<footer class="footer">
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-section">
        <h3>Quadrate Technologies</h3>
        <p>Leading enterprise technology transformation through innovative solutions and strategic partnerships.</p>
      </div>
      <div class="footer-section">
        <h4>Solutions</h4>
        <ul>
          <li><a routerLink="/solutions">Enterprise Solutions</a></li>
          <li><a routerLink="/products">Software Products</a></li>
          <li><a routerLink="/industries">Industry Expertise</a></li>
        </ul>
      </div>
      <div class="footer-section">
        <h4>Company</h4>
        <ul>
          <li><a routerLink="/about">About</a></li>
          <li><a routerLink="/support">Support</a></li>
          <li><a routerLink="/careers">Careers</a></li>
          <li><a routerLink="/resources">Resources</a></li>
        </ul>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2024 Quadrate Technologies. All rights reserved.</p>
    </div>
  </div>
</footer>
</div>
<!-- End App Container -->