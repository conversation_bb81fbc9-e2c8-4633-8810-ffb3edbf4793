import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterLink } from '@angular/router';
import { SEOService } from '../../services/seo.service';

@Component({
  selector: 'app-solutions',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatTabsModule, MatIconModule, MatButtonModule, RouterLink],
  template: `
    <div class="solutions-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Enterprise Solutions That Drive Digital Transformation</h1>
          <p class="hero-subtitle">
            Comprehensive technology solutions designed for enterprise scale and performance.
            From cloud migration to AI implementation, we deliver solutions that accelerate innovation and drive measurable business outcomes.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">500+</span>
              <span class="stat-label">Enterprise Deployments</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">Uptime SLA</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">$50M+</span>
              <span class="stat-label">Cost Savings Delivered</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Solutions Overview -->
      <div class="solutions-overview">
        <div class="container">
          <h2>Comprehensive Enterprise Solutions</h2>
          <p class="section-subtitle">
            End-to-end technology solutions that address your most complex business challenges
            and position your organization for sustained growth and competitive advantage.
          </p>

          <div class="value-proposition">
            <div class="value-item">
              <mat-icon>rocket_launch</mat-icon>
              <h4>Accelerate Innovation</h4>
              <p>Reduce time-to-market by 60% with modern development practices and cloud-native architectures</p>
            </div>
            <div class="value-item">
              <mat-icon>analytics</mat-icon>
              <h4>Data-Driven Insights</h4>
              <p>Transform raw data into actionable intelligence with advanced analytics and AI capabilities</p>
            </div>
            <div class="value-item">
              <mat-icon>shield</mat-icon>
              <h4>Enterprise Security</h4>
              <p>Protect your assets with enterprise-grade security frameworks and compliance management</p>
            </div>
          </div>

          <mat-tab-group class="solution-tabs" animationDuration="300ms">
            @for (category of solutionCategories; track category.name) {
              <mat-tab [label]="category.name">
                <div class="tab-content">
                  <div class="category-intro">
                    <h3>{{ category.title }}</h3>
                    <p>{{ category.description }}</p>
                  </div>
                  <div class="solutions-grid">
                    @for (solution of category.solutions; track solution.title) {
                      <mat-card class="solution-card" [style.border-top-color]="solution.color">
                        <mat-card-header>
                          <mat-icon [style.color]="solution.color" mat-card-avatar>{{ solution.icon }}</mat-icon>
                          <mat-card-title>{{ solution.title }}</mat-card-title>
                          <mat-card-subtitle>{{ solution.subtitle }}</mat-card-subtitle>
                        </mat-card-header>
                        <mat-card-content>
                          <p>{{ solution.description }}</p>
                          <div class="solution-features">
                            <h5>Key Capabilities:</h5>
                            <ul>
                              @for (feature of solution.features; track feature) {
                                <li>{{ feature }}</li>
                              }
                            </ul>
                          </div>
                          <div class="solution-benefits">
                            <h5>Business Impact:</h5>
                            <div class="benefits-list">
                              @for (benefit of solution.benefits; track benefit) {
                                <span class="benefit-tag">{{ benefit }}</span>
                              }
                            </div>
                          </div>
                        </mat-card-content>
                        <mat-card-actions>
                          <button mat-raised-button color="primary">Learn More</button>
                          <button mat-stroked-button routerLink="/support">Request Demo</button>
                        </mat-card-actions>
                      </mat-card>
                    }
                  </div>
                </div>
              </mat-tab>
            }
          </mat-tab-group>
        </div>
      </div>

      <!-- Implementation Approach -->
      <div class="implementation-section">
        <div class="container">
          <h2>Our Implementation Approach</h2>
          <p class="section-subtitle">Proven methodology that ensures successful delivery and adoption</p>
          <div class="implementation-steps">
            <div class="step-item">
              <div class="step-number">1</div>
              <h4>Assessment & Strategy</h4>
              <p>Comprehensive analysis of your current state and strategic roadmap development</p>
            </div>
            <div class="step-item">
              <div class="step-number">2</div>
              <h4>Design & Architecture</h4>
              <p>Detailed solution design with scalable architecture and security considerations</p>
            </div>
            <div class="step-item">
              <div class="step-number">3</div>
              <h4>Development & Testing</h4>
              <p>Agile development with continuous testing and quality assurance</p>
            </div>
            <div class="step-item">
              <div class="step-number">4</div>
              <h4>Deployment & Support</h4>
              <p>Seamless deployment with comprehensive training and ongoing support</p>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Transform Your Enterprise?</h2>
          <p>Let's discuss how our solutions can address your specific business challenges and drive growth.</p>
          <div class="cta-actions">
            <button mat-raised-button color="primary" routerLink="/support">Schedule Consultation</button>
            <button mat-stroked-button routerLink="/case-studies">View Success Stories</button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .solutions-page {
      min-height: 100vh;
    }

    .hero-section {
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      padding: 120px 0 80px;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      line-height: 1.2;
    }

    .hero-subtitle {
      font-size: 1.25rem;
      margin-bottom: 3rem;
      opacity: 0.9;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: 4rem;
      margin-top: 3rem;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .solutions-overview {
      padding: 80px 0;
    }

    .solutions-overview h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .section-subtitle {
      text-align: center;
      margin-bottom: 3rem;
      font-size: 1.1rem;
      color: #666;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .value-proposition {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 4rem;
    }

    .value-item {
      text-align: center;
      padding: 2rem;
      background: #f8f9fa;
      border-radius: 12px;
    }

    .value-item mat-icon {
      font-size: 3rem;
      color: var(--primary-blue);
      margin-bottom: 1rem;
    }

    .value-item h4 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--primary-black);
    }

    .value-item p {
      color: #666;
      line-height: 1.6;
    }

    .solution-tabs {
      margin-top: 2rem;
    }

    .tab-content {
      padding: 2rem 0;
    }

    .category-intro {
      text-align: center;
      margin-bottom: 3rem;
    }

    .category-intro h3 {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .category-intro p {
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
    }

    .solutions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .solution-card {
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-top: 4px solid transparent;
    }

    .solution-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .solution-features h5, .solution-benefits h5 {
      font-size: 0.9rem;
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: var(--primary-black);
    }

    .solution-features ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .solution-features li {
      padding: 0.25rem 0;
      position: relative;
      padding-left: 1.5rem;
      color: #666;
      font-size: 0.9rem;
    }

    .solution-features li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: var(--nlp-green);
      font-weight: bold;
    }

    .benefits-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .benefit-tag {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .implementation-section {
      padding: 80px 0;
      background: #f8f9fa;
    }

    .implementation-section h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .implementation-steps {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .step-item {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .step-number {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 auto 1.5rem;
    }

    .step-item h4 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--primary-black);
    }

    .step-item p {
      color: #666;
      line-height: 1.6;
    }

    .cta-section {
      padding: 80px 0;
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2.5rem;
      opacity: 0.9;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-actions {
      display: flex;
      gap: 1.5rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .hero-section {
        padding: 4rem 0;
      }

      .hero-section h1 {
        font-size: 2.8rem;
        line-height: 1.2;
      }

      .hero-section p {
        font-size: 1.1rem;
      }

      .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
      }

      .stat-item {
        padding: 1rem;
        min-width: auto;
      }

      .solutions-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .solution-card {
        padding: 1.5rem;
      }

      .value-proposition {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .implementation-steps {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .cta-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }

      .container {
        padding: 0 1rem;
      }
    }

    @media (max-width: 480px) {
      .hero-section h1 {
        font-size: 2.2rem;
      }

      .hero-section p {
        font-size: 1rem;
      }

      .solution-card {
        padding: 1.2rem;
      }

      .solution-card h3 {
        font-size: 1.3rem;
      }

      .stat-item {
        padding: 0.8rem;
      }
    }
  `]
})
export class SolutionsComponent implements OnInit {
  solutionCategories = [
    {
      name: 'Cloud & Infrastructure',
      title: 'Cloud Migration & Modernization',
      description: 'Transform your IT infrastructure with enterprise-grade cloud solutions that scale with your business growth.',
      solutions: [
        {
          title: 'Azure Cloud Migration',
          subtitle: 'Enterprise-grade cloud transformation',
          description: 'Comprehensive migration strategy and implementation for seamless transition to Microsoft Azure cloud platform.',
          icon: 'cloud_upload',
          color: '#0078D4',
          features: ['Assessment & Planning', 'Data Migration', 'Application Modernization', 'Security Implementation'],
          benefits: ['60% Cost Reduction', 'Enhanced Security', 'Improved Scalability']
        },
        {
          title: 'AWS Infrastructure',
          subtitle: 'Scalable cloud architecture',
          description: 'Design and implement robust AWS infrastructure with auto-scaling, high availability, and disaster recovery.',
          icon: 'architecture',
          color: '#FF9900',
          features: ['Infrastructure as Code', 'Auto-scaling', 'Load Balancing', 'Monitoring & Alerts'],
          benefits: ['99.9% Uptime', 'Cost Optimization', 'Global Reach']
        },
        {
          title: 'Hybrid Cloud Solutions',
          subtitle: 'Best of both worlds',
          description: 'Seamlessly integrate on-premises and cloud environments for optimal performance and flexibility.',
          icon: 'hub',
          color: '#4CAF50',
          features: ['Hybrid Connectivity', 'Data Synchronization', 'Unified Management', 'Security Compliance'],
          benefits: ['Flexibility', 'Cost Control', 'Compliance Ready']
        }
      ]
    },
    {
      name: 'Enterprise Applications',
      title: 'Custom Enterprise Software',
      description: 'Build scalable, secure applications that drive business growth and operational excellence.',
      solutions: [
        {
          title: 'ERP Systems',
          subtitle: 'Integrated business management',
          description: 'Custom ERP solutions that unify your business processes and provide real-time visibility across operations.',
          icon: 'business_center',
          color: '#0607E1',
          features: ['Financial Management', 'Supply Chain', 'HR & Payroll', 'Reporting & Analytics'],
          benefits: ['Operational Efficiency', 'Cost Reduction', 'Better Decision Making']
        },
        {
          title: 'CRM Platforms',
          subtitle: 'Customer relationship excellence',
          description: 'Advanced CRM systems with AI-powered insights for enhanced customer engagement and sales performance.',
          icon: 'people',
          color: '#E91E63',
          features: ['Lead Management', 'Sales Automation', 'Customer Analytics', 'Mobile Access'],
          benefits: ['Increased Sales', 'Better Customer Retention', 'Improved Productivity']
        },
        {
          title: 'Workflow Automation',
          subtitle: 'Streamlined operations',
          description: 'Intelligent workflow automation that eliminates manual processes and reduces operational overhead.',
          icon: 'autorenew',
          color: '#FF9800',
          features: ['Process Mapping', 'Automated Workflows', 'Integration APIs', 'Performance Monitoring'],
          benefits: ['40% Time Savings', 'Reduced Errors', 'Improved Compliance']
        }
      ]
    },
    {
      name: 'AI & Analytics',
      title: 'Artificial Intelligence Solutions',
      description: 'Harness the power of AI and machine learning to unlock insights and automate complex processes.',
      solutions: [
        {
          title: 'Predictive Analytics',
          subtitle: 'Future-ready insights',
          description: 'Advanced analytics platforms that predict trends, identify opportunities, and mitigate risks.',
          icon: 'trending_up',
          color: '#9C27B0',
          features: ['Machine Learning Models', 'Real-time Analytics', 'Predictive Modeling', 'Data Visualization'],
          benefits: ['Better Forecasting', 'Risk Mitigation', 'Competitive Advantage']
        },
        {
          title: 'Natural Language Processing',
          subtitle: 'Intelligent text analysis',
          description: 'NLP solutions for document processing, sentiment analysis, and automated content generation.',
          icon: 'psychology',
          color: '#4D0AFF',
          features: ['Text Analytics', 'Sentiment Analysis', 'Document Processing', 'Chatbot Integration'],
          benefits: ['Automated Processing', 'Improved Accuracy', 'Enhanced User Experience']
        },
        {
          title: 'Computer Vision',
          subtitle: 'Visual intelligence',
          description: 'AI-powered image and video analysis for quality control, security, and process optimization.',
          icon: 'visibility',
          color: '#00BCD4',
          features: ['Image Recognition', 'Object Detection', 'Quality Inspection', 'Real-time Processing'],
          benefits: ['Quality Improvement', 'Cost Reduction', 'Automated Inspection']
        }
      ]
    },
    {
      name: 'Security & Compliance',
      title: 'Cybersecurity Solutions',
      description: 'Comprehensive security frameworks that protect your enterprise assets and ensure regulatory compliance.',
      solutions: [
        {
          title: 'Security Assessment',
          subtitle: 'Comprehensive security audit',
          description: 'Thorough security assessments to identify vulnerabilities and strengthen your security posture.',
          icon: 'security',
          color: '#F44336',
          features: ['Vulnerability Assessment', 'Penetration Testing', 'Risk Analysis', 'Compliance Review'],
          benefits: ['Enhanced Security', 'Risk Reduction', 'Compliance Assurance']
        },
        {
          title: 'Identity Management',
          subtitle: 'Secure access control',
          description: 'Advanced identity and access management solutions with multi-factor authentication and SSO.',
          icon: 'verified_user',
          color: '#795548',
          features: ['Single Sign-On', 'Multi-factor Authentication', 'Role-based Access', 'Audit Trails'],
          benefits: ['Improved Security', 'User Convenience', 'Compliance Ready']
        },
        {
          title: 'Threat Detection',
          subtitle: 'Proactive security monitoring',
          description: 'AI-powered threat detection and response systems that protect against advanced cyber threats.',
          icon: 'shield',
          color: '#607D8B',
          features: ['Real-time Monitoring', 'Threat Intelligence', 'Automated Response', 'Incident Management'],
          benefits: ['Faster Response', 'Reduced Risk', 'Continuous Protection']
        }
      ]
    }
  ];

  constructor(private seoService: SEOService) {}

  ngOnInit() {
    this.seoService.updateSEO({
      title: 'Enterprise Solutions - Quadrate Technologies',
      description: 'Comprehensive enterprise technology solutions including cloud migration, AI implementation, and digital transformation services.',
      keywords: 'enterprise solutions, cloud migration, digital transformation, AI implementation, custom software development'
    });
  }
}
