import { <PERSON><PERSON>nent, OnInit, On<PERSON><PERSON>roy, ElementRef, ViewChild, AfterViewInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';

interface TechNode {
  id: string;
  name: string;
  icon: string;
  color: string;
  angle: number;
  radius: number;
  x: number;
  y: number;
  scale: number;
  opacity: number;
}

@Component({
  selector: 'app-hero-animation',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="hero-animation-container" #container>
      <div class="animation-canvas">
        <!-- Central CPU Core -->
        <div class="cpu-core" [style.transform]="'rotate(' + coreRotation + 'deg)'">
          <div class="cpu-inner">
            <div class="cpu-circuits"></div>
            <div class="cpu-logo">QTS</div>
          </div>
        </div>

        <!-- Technology Nodes -->
        <div class="tech-nodes">
          @for (node of techNodes; track node.id) {
            <div
              class="tech-node"
              [style.transform]="'translate(' + node.x + 'px, ' + node.y + 'px) scale(' + node.scale + ')'"
              [style.opacity]="node.opacity"
              [style.background]="node.color"
              (mouseenter)="onNodeHover(node)"
              (mouseleave)="onNodeLeave(node)"
              [attr.data-tooltip]="node.name">
              <i class="node-icon" [innerHTML]="getIconSvg(node.icon)"></i>
            </div>
          }
        </div>

        <!-- Connecting Lines -->
        <svg class="connection-lines" width="100%" height="100%">
          @for (node of techNodes; track node.id) {
            <line
              [attr.x1]="centerX"
              [attr.y1]="centerY"
              [attr.x2]="centerX + node.x"
              [attr.y2]="centerY + node.y"
              class="connection-line"
              [style.opacity]="node.opacity * 0.3">
            </line>
          }
        </svg>

        <!-- Floating Particles -->
        <div class="particles">
          @for (particle of particles; track particle.id) {
            <div
              class="particle"
              [style.transform]="'translate(' + particle.x + 'px, ' + particle.y + 'px)'"
              [style.opacity]="particle.opacity">
            </div>
          }
        </div>

        <!-- Interactive Tooltip -->
        @if (hoveredNode) {
          <div class="node-tooltip"
               [style.transform]="'translate(' + tooltipX + 'px, ' + tooltipY + 'px)'">
            <h4>{{ hoveredNode.name }}</h4>
            <p>{{ getNodeDescription(hoveredNode.name) }}</p>
          </div>
        }
      </div>
    </div>
  `,
  styles: [`
    .hero-animation-container {
      position: relative;
      width: 100%;
      height: 500px;
      overflow: hidden;
      background: radial-gradient(circle at center, rgba(6, 7, 225, 0.1) 0%, transparent 70%);
    }

    .animation-canvas {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .cpu-core {
      position: absolute;
      width: 120px;
      height: 120px;
      z-index: 10;
      animation: pulse 3s ease-in-out infinite;
    }

    .cpu-inner {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      box-shadow: 0 0 30px rgba(6, 7, 225, 0.5);
      border: 3px solid rgba(255, 255, 255, 0.2);
    }

    .cpu-circuits {
      position: absolute;
      inset: 10px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
    }

    .cpu-circuits::before,
    .cpu-circuits::after {
      content: '';
      position: absolute;
      background: rgba(255, 255, 255, 0.4);
    }

    .cpu-circuits::before {
      width: 20px;
      height: 2px;
      top: 20px;
      left: 10px;
    }

    .cpu-circuits::after {
      width: 2px;
      height: 20px;
      top: 10px;
      right: 20px;
    }

    .cpu-logo {
      color: white;
      font-weight: 800;
      font-size: 18px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .tech-nodes {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    .tech-node {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
    }

    .tech-node:hover {
      transform: scale(1.2) !important;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .node-icon {
      color: white;
      font-size: 24px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    }

    .connection-lines {
      position: absolute;
      top: 0;
      left: 0;
      pointer-events: none;
      z-index: 1;
    }

    .connection-line {
      stroke: rgba(6, 7, 225, 0.4);
      stroke-width: 2;
      stroke-dasharray: 5, 5;
      animation: dash 2s linear infinite;
    }

    .particles {
      position: absolute;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: #06B6D4;
      border-radius: 50%;
      box-shadow: 0 0 10px #06B6D4;
    }

    .node-tooltip {
      position: absolute;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      font-size: 14px;
      max-width: 200px;
      z-index: 20;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      pointer-events: none;
    }

    .node-tooltip h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: #06B6D4;
    }

    .node-tooltip p {
      margin: 0;
      font-size: 12px;
      opacity: 0.9;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    @keyframes dash {
      to { stroke-dashoffset: -10; }
    }

    @media (max-width: 768px) {
      .hero-animation-container {
        height: 400px;
      }

      .cpu-core {
        width: 80px;
        height: 80px;
      }

      .cpu-logo {
        font-size: 14px;
      }

      .tech-node {
        width: 40px;
        height: 40px;
      }

      .node-icon {
        font-size: 18px;
      }
    }

    @media (prefers-reduced-motion: reduce) {
      .cpu-core,
      .connection-line,
      .tech-node {
        animation: none;
      }
    }
  `]
})
export class HeroAnimationComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('container', { static: true }) container!: ElementRef;

  coreRotation = 0;
  centerX = 0;
  centerY = 0;
  hoveredNode: TechNode | null = null;
  tooltipX = 0;
  tooltipY = 0;

  private animationId: number = 0;
  private mouseX = 0;
  private mouseY = 0;

  techNodes: TechNode[] = [
    { id: '1', name: 'AI Strategy', icon: 'brain', color: '#0607E1', angle: 0, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '2', name: 'Generative AI', icon: 'bot', color: '#4D0AFF', angle: 30, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '3', name: 'Computer Vision', icon: 'eye', color: '#06B6D4', angle: 60, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '4', name: 'NLP', icon: 'message', color: '#10B981', angle: 90, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '5', name: 'Development', icon: 'code', color: '#F59E0B', angle: 120, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '6', name: 'Data Engineering', icon: 'database', color: '#EF4444', angle: 150, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '7', name: 'Cloud Solutions', icon: 'cloud', color: '#8B5CF6', angle: 180, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '8', name: 'Web Solutions', icon: 'globe', color: '#06B6D4', angle: 210, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '9', name: 'Integration', icon: 'network', color: '#10B981', angle: 240, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '10', name: 'Analytics', icon: 'chart', color: '#F59E0B', angle: 270, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '11', name: 'Automation', icon: 'settings', color: '#EF4444', angle: 300, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 },
    { id: '12', name: 'Architecture', icon: 'layers', color: '#8B5CF6', angle: 330, radius: 180, x: 0, y: 0, scale: 1, opacity: 1 }
  ];

  particles: Array<{id: string, x: number, y: number, opacity: number, vx: number, vy: number}> = [];

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.initializeParticles();
      this.startAnimation();
    }
  }

  ngAfterViewInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.updateDimensions();
      this.updateNodePositions();

      // Add mouse move listener for interactivity
      if (this.container?.nativeElement) {
        this.container.nativeElement.addEventListener('mousemove', this.onMouseMove.bind(this));
      }
    }
  }

  ngOnDestroy() {
    if (isPlatformBrowser(this.platformId) && this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }

  private updateDimensions() {
    if (isPlatformBrowser(this.platformId) && this.container?.nativeElement) {
      const rect = this.container.nativeElement.getBoundingClientRect();
      this.centerX = rect.width / 2;
      this.centerY = rect.height / 2;
    } else {
      // Default dimensions for SSR
      this.centerX = 250;
      this.centerY = 250;
    }
  }

  private updateNodePositions() {
    this.techNodes.forEach(node => {
      const adjustedAngle = node.angle + this.coreRotation * 0.5;
      const radian = (adjustedAngle * Math.PI) / 180;
      node.x = Math.cos(radian) * node.radius;
      node.y = Math.sin(radian) * node.radius;
    });
  }

  private startAnimation() {
    if (!isPlatformBrowser(this.platformId)) return;

    const animate = () => {
      this.coreRotation += 0.5;
      this.updateNodePositions();
      this.updateParticles();

      this.animationId = requestAnimationFrame(animate);
    };
    animate();
  }

  private initializeParticles() {
    for (let i = 0; i < 20; i++) {
      this.particles.push({
        id: `particle-${i}`,
        x: Math.random() * 500,
        y: Math.random() * 500,
        opacity: Math.random() * 0.5 + 0.2,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5
      });
    }
  }

  private updateParticles() {
    this.particles.forEach(particle => {
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Wrap around edges
      if (particle.x < 0) particle.x = 500;
      if (particle.x > 500) particle.x = 0;
      if (particle.y < 0) particle.y = 500;
      if (particle.y > 500) particle.y = 0;
    });
  }

  onNodeHover(node: TechNode) {
    this.hoveredNode = node;
    node.scale = 1.2;
  }

  onNodeLeave(node: TechNode) {
    this.hoveredNode = null;
    node.scale = 1;
  }

  private onMouseMove(event: MouseEvent) {
    if (!isPlatformBrowser(this.platformId) || !this.container?.nativeElement) return;

    const rect = this.container.nativeElement.getBoundingClientRect();
    this.mouseX = event.clientX - rect.left;
    this.mouseY = event.clientY - rect.top;

    if (this.hoveredNode) {
      this.tooltipX = this.mouseX + 10;
      this.tooltipY = this.mouseY - 10;
    }
  }

  getIconSvg(iconName: string): string {
    const icons: {[key: string]: string} = {
      'brain': '🧠',
      'bot': '🤖',
      'eye': '👁️',
      'message': '💬',
      'code': '💻',
      'database': '🗄️',
      'cloud': '☁️',
      'globe': '🌐',
      'network': '🔗',
      'chart': '📊',
      'settings': '⚙️',
      'layers': '📚'
    };
    return icons[iconName] || '⭐';
  }

  getNodeDescription(nodeName: string): string {
    const descriptions: {[key: string]: string} = {
      'AI Strategy': 'Strategic guidance for AI adoption and implementation',
      'Generative AI': 'Advanced content and code generation solutions',
      'Computer Vision': 'Image recognition and video analysis systems',
      'NLP': 'Natural language processing and chatbot development',
      'Development': 'Custom software development and applications',
      'Data Engineering': 'Data collection, processing, and pipeline management',
      'Cloud Solutions': 'Scalable cloud infrastructure and migration',
      'Web Solutions': 'Modern web applications and digital platforms',
      'Integration': 'Seamless system integration and API development',
      'Analytics': 'Business intelligence and data visualization',
      'Automation': 'Process automation and workflow optimization',
      'Architecture': 'System design and technical architecture'
    };
    return descriptions[nodeName] || 'Advanced technology solution';
  }
}
