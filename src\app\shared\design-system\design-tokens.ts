// Design System Tokens for QTS Angular Application
// Following Material Design 3.0 principles with custom QTS branding

export interface ColorToken {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}

export interface TypographyToken {
  fontFamily: string;
  fontSize: string;
  fontWeight: number;
  lineHeight: string;
  letterSpacing: string;
}

export interface SpacingToken {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
}

export interface ShadowToken {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
}

export interface BorderRadiusToken {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  full: string;
}

export interface AnimationToken {
  duration: {
    fast: string;
    normal: string;
    slow: string;
  };
  easing: {
    linear: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
    bounce: string;
  };
}

// QTS Brand Colors
export const colors = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#0607E1', // QTS Primary Blue
    600: '#0506b8',
    700: '#04058f',
    800: '#030466',
    900: '#02033d',
    950: '#010214'
  } as ColorToken,
  
  secondary: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#4D0AFF', // QTS Generative Purple
    600: '#4108d9',
    700: '#3506b3',
    800: '#29048d',
    900: '#1d0367',
    950: '#110241'
  } as ColorToken,
  
  accent: {
    50: '#ecfeff',
    100: '#cffafe',
    200: '#a5f3fc',
    300: '#67e8f9',
    400: '#22d3ee',
    500: '#06B6D4', // QTS Vision Cyan
    600: '#0591ae',
    700: '#046c88',
    800: '#034762',
    900: '#02223c',
    950: '#011116'
  } as ColorToken,
  
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#10B981', // QTS NLP Green
    600: '#0e9f6e',
    700: '#0c7f5b',
    800: '#0a5f48',
    900: '#083f35',
    950: '#061f22'
  } as ColorToken,
  
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#F59E0B', // QTS Dev Orange
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03'
  } as ColorToken,
  
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#EF4444', // QTS Data Red
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a'
  } as ColorToken,
  
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0a0a0a'
  } as ColorToken
};

// Typography Scale
export const typography = {
  display: {
    large: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '3.75rem',
      fontWeight: 800,
      lineHeight: '1.1',
      letterSpacing: '-0.025em'
    },
    medium: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '3rem',
      fontWeight: 700,
      lineHeight: '1.2',
      letterSpacing: '-0.02em'
    },
    small: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '2.25rem',
      fontWeight: 600,
      lineHeight: '1.3',
      letterSpacing: '-0.015em'
    }
  },
  headline: {
    large: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: '1.4',
      letterSpacing: '0'
    },
    medium: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: '1.4',
      letterSpacing: '0'
    },
    small: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: '1.5',
      letterSpacing: '0'
    }
  },
  title: {
    large: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '1.375rem',
      fontWeight: 500,
      lineHeight: '1.5',
      letterSpacing: '0'
    },
    medium: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: '1.5',
      letterSpacing: '0.01em'
    },
    small: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: '1.6',
      letterSpacing: '0.01em'
    }
  },
  body: {
    large: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: '1.6',
      letterSpacing: '0.01em'
    },
    medium: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: '1.6',
      letterSpacing: '0.025em'
    },
    small: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: '1.7',
      letterSpacing: '0.04em'
    }
  },
  label: {
    large: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: '1.4',
      letterSpacing: '0.01em'
    },
    medium: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: '1.3',
      letterSpacing: '0.05em'
    },
    small: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '0.6875rem',
      fontWeight: 500,
      lineHeight: '1.3',
      letterSpacing: '0.05em'
    }
  }
};

// Spacing Scale
export const spacing: SpacingToken = {
  xs: '0.25rem',   // 4px
  sm: '0.5rem',    // 8px
  md: '1rem',      // 16px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '3rem',   // 48px
  '3xl': '4rem',   // 64px
  '4xl': '6rem'    // 96px
};

// Shadow Scale
export const shadows: ShadowToken = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'
};

// Border Radius Scale
export const borderRadius: BorderRadiusToken = {
  none: '0',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  '3xl': '1.5rem',
  full: '9999px'
};

// Animation Tokens
export const animations: AnimationToken = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms'
  },
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
  }
};

// Breakpoints
export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

// Z-Index Scale
export const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800
};
