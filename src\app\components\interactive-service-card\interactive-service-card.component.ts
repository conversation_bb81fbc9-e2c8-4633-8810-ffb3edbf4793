import { Component, Input, OnInit, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';

interface ServiceData {
  title: string;
  description: string;
  benefits: string[];
  icon: string;
  color: string;
  gradient: string;
  features?: string[];
}

@Component({
  selector: 'app-interactive-service-card',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, RouterLink],
  template: `
    <mat-card 
      class="service-card"
      [class.expanded]="isExpanded"
      (mouseenter)="onHover(true)"
      (mouseleave)="onHover(false)"
      #cardElement>
      
      <div class="card-header" [style.background]="service.gradient">
        <div class="service-icon" [style.color]="service.color">
          <mat-icon>{{ service.icon }}</mat-icon>
        </div>
        <div class="service-title">
          <h3>{{ service.title }}</h3>
        </div>
      </div>
      
      <mat-card-content class="card-content">
        <p class="service-description">{{ service.description }}</p>
        
        <div class="benefits-section" [class.visible]="showBenefits">
          <h4>Key Benefits:</h4>
          <ul class="benefits-list">
            @for (benefit of service.benefits; track benefit; let i = $index) {
              <li class="benefit-item stagger-child" [style.animation-delay]="(i * 0.1) + 's'">
                <mat-icon class="benefit-icon">check_circle</mat-icon>
                {{ benefit }}
              </li>
            }
          </ul>
        </div>
        
        @if (service.features && service.features.length > 0) {
          <div class="features-section" [class.visible]="showFeatures">
            <h4>Features:</h4>
            <div class="features-grid">
              @for (feature of service.features; track feature; let i = $index) {
                <div class="feature-tag stagger-child" [style.animation-delay]="(i * 0.1) + 's'">
                  {{ feature }}
                </div>
              }
            </div>
          </div>
        }
      </mat-card-content>
      
      <div class="card-actions">
        <button 
          mat-raised-button 
          color="primary" 
          class="learn-more-btn"
          (click)="toggleExpanded()"
          [style.background]="service.gradient">
          {{ isExpanded ? 'Show Less' : 'Learn More' }}
          <mat-icon>{{ isExpanded ? 'expand_less' : 'expand_more' }}</mat-icon>
        </button>
        
        <button 
          mat-stroked-button 
          routerLink="/contact"
          class="contact-btn"
          [style.border-color]="service.color"
          [style.color]="service.color">
          Get Quote
        </button>
      </div>
      
      <!-- Animated background elements -->
      <div class="bg-animation">
        @for (dot of backgroundDots; track dot.id) {
          <div 
            class="bg-dot"
            [style.left]="dot.x + '%'"
            [style.top]="dot.y + '%'"
            [style.animation-delay]="dot.delay + 's'"
            [style.background]="service.color">
          </div>
        }
      </div>
    </mat-card>
  `,
  styles: [`
    .service-card {
      position: relative;
      height: 100%;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      border-radius: 16px !important;
      cursor: pointer;
      background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    }
    
    .service-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    }
    
    .service-card.expanded {
      transform: scale(1.05);
    }
    
    .card-header {
      padding: 2rem;
      color: white;
      position: relative;
      overflow: hidden;
    }
    
    .card-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
    }
    
    .service-icon {
      position: relative;
      z-index: 2;
      margin-bottom: 1rem;
    }
    
    .service-icon mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
      transition: transform 0.3s ease;
    }
    
    .service-card:hover .service-icon mat-icon {
      transform: scale(1.1) rotate(5deg);
    }
    
    .service-title {
      position: relative;
      z-index: 2;
    }
    
    .service-title h3 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .card-content {
      padding: 1.5rem 2rem !important;
    }
    
    .service-description {
      font-size: 1rem;
      line-height: 1.6;
      color: #666;
      margin-bottom: 1.5rem;
    }
    
    .benefits-section,
    .features-section {
      opacity: 0;
      max-height: 0;
      overflow: hidden;
      transition: all 0.4s ease;
    }
    
    .benefits-section.visible,
    .features-section.visible {
      opacity: 1;
      max-height: 300px;
      margin-bottom: 1.5rem;
    }
    
    .benefits-section h4,
    .features-section h4 {
      color: #333;
      font-weight: 600;
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }
    
    .benefits-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .benefit-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
      opacity: 0;
      transform: translateX(-20px);
      animation: slideInLeft 0.5s ease forwards;
    }
    
    .benefit-item.stagger-child.visible {
      animation: slideInLeft 0.5s ease forwards;
    }
    
    .benefit-icon {
      color: #10B981;
      margin-right: 0.75rem;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
    
    .features-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }
    
    .feature-tag {
      background: rgba(6, 7, 225, 0.1);
      color: #0607E1;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
      opacity: 0;
      transform: scale(0.8);
      animation: scaleIn 0.4s ease forwards;
    }
    
    .feature-tag.stagger-child.visible {
      animation: scaleIn 0.4s ease forwards;
    }
    
    .card-actions {
      padding: 1rem 2rem 2rem;
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }
    
    .learn-more-btn,
    .contact-btn {
      flex: 1;
      min-width: 120px;
      border-radius: 8px !important;
      font-weight: 600 !important;
      text-transform: none !important;
      transition: all 0.3s ease !important;
    }
    
    .learn-more-btn {
      color: white !important;
    }
    
    .learn-more-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }
    
    .contact-btn:hover {
      transform: translateY(-2px);
      background: rgba(6, 7, 225, 0.1) !important;
    }
    
    .bg-animation {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      overflow: hidden;
    }
    
    .bg-dot {
      position: absolute;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      opacity: 0.3;
      animation: float 3s ease-in-out infinite;
    }
    
    @keyframes slideInLeft {
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
    
    @keyframes scaleIn {
      to {
        opacity: 1;
        transform: scale(1);
      }
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    
    @media (max-width: 768px) {
      .card-header {
        padding: 1.5rem;
      }
      
      .card-content {
        padding: 1rem 1.5rem !important;
      }
      
      .card-actions {
        padding: 1rem 1.5rem 1.5rem;
        flex-direction: column;
      }
      
      .service-card:hover {
        transform: translateY(-4px);
      }
    }
    
    @media (prefers-reduced-motion: reduce) {
      .service-card,
      .service-icon mat-icon,
      .benefit-item,
      .feature-tag,
      .bg-dot {
        animation: none !important;
        transition: none !important;
      }
    }
  `]
})
export class InteractiveServiceCardComponent implements OnInit {
  @Input() service!: ServiceData;
  @ViewChild('cardElement') cardElement!: ElementRef;
  
  isExpanded = false;
  showBenefits = false;
  showFeatures = false;
  backgroundDots: Array<{id: number, x: number, y: number, delay: number}> = [];
  
  ngOnInit() {
    this.generateBackgroundDots();
  }
  
  private generateBackgroundDots() {
    for (let i = 0; i < 6; i++) {
      this.backgroundDots.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 2
      });
    }
  }
  
  onHover(isHovering: boolean) {
    if (isHovering && !this.isExpanded) {
      this.showBenefits = true;
    }
  }
  
  toggleExpanded() {
    this.isExpanded = !this.isExpanded;
    this.showBenefits = this.isExpanded;
    this.showFeatures = this.isExpanded;
    
    if (this.isExpanded) {
      setTimeout(() => {
        const benefitItems = this.cardElement.nativeElement.querySelectorAll('.benefit-item');
        const featureTags = this.cardElement.nativeElement.querySelectorAll('.feature-tag');
        
        benefitItems.forEach((item: Element) => item.classList.add('visible'));
        featureTags.forEach((tag: Element) => tag.classList.add('visible'));
      }, 100);
    }
  }
}
