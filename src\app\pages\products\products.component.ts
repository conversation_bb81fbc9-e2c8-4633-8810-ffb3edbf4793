import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterLink } from '@angular/router';
import { SEOService } from '../../services/seo.service';

interface Product {
  name: string;
  description: string;
  features: string[];
  icon: string;
  category: string;
  status: 'Available' | 'Coming Soon' | 'Beta';
  pricing: string;
}

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatTabsModule, RouterLink],
  template: `
    <div class="products-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Enterprise Software Products</h1>
          <p class="hero-subtitle">
            Discover our comprehensive suite of enterprise software products designed to accelerate 
            digital transformation and drive business innovation across industries.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">50+</span>
              <span class="stat-label">Enterprise Products</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">1M+</span>
              <span class="stat-label">Active Users</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">Uptime SLA</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Categories -->
      <div class="products-section">
        <div class="container">
          <mat-tab-group class="product-tabs">
            <mat-tab label="Business Applications">
              <div class="products-grid">
                @for (product of businessProducts; track product.name) {
                  <mat-card class="product-card">
                    <mat-card-header>
                      <mat-icon [class]="'product-icon ' + product.category">{{ product.icon }}</mat-icon>
                      <mat-card-title>{{ product.name }}</mat-card-title>
                      <mat-card-subtitle>
                        <span class="status-badge" [class]="product.status.toLowerCase().replace(' ', '-')">
                          {{ product.status }}
                        </span>
                      </mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ product.description }}</p>
                      <ul class="feature-list">
                        @for (feature of product.features; track feature) {
                          <li>{{ feature }}</li>
                        }
                      </ul>
                      <div class="pricing">
                        <span class="price">{{ product.pricing }}</span>
                      </div>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">Learn More</button>
                      <button mat-stroked-button routerLink="/support">Contact Sales</button>
                    </mat-card-actions>
                  </mat-card>
                }
              </div>
            </mat-tab>

            <mat-tab label="Cloud Platforms">
              <div class="products-grid">
                @for (product of cloudProducts; track product.name) {
                  <mat-card class="product-card">
                    <mat-card-header>
                      <mat-icon [class]="'product-icon ' + product.category">{{ product.icon }}</mat-icon>
                      <mat-card-title>{{ product.name }}</mat-card-title>
                      <mat-card-subtitle>
                        <span class="status-badge" [class]="product.status.toLowerCase().replace(' ', '-')">
                          {{ product.status }}
                        </span>
                      </mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ product.description }}</p>
                      <ul class="feature-list">
                        @for (feature of product.features; track feature) {
                          <li>{{ feature }}</li>
                        }
                      </ul>
                      <div class="pricing">
                        <span class="price">{{ product.pricing }}</span>
                      </div>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">Learn More</button>
                      <button mat-stroked-button routerLink="/support">Contact Sales</button>
                    </mat-card-actions>
                  </mat-card>
                }
              </div>
            </mat-tab>

            <mat-tab label="AI & Analytics">
              <div class="products-grid">
                @for (product of aiProducts; track product.name) {
                  <mat-card class="product-card">
                    <mat-card-header>
                      <mat-icon [class]="'product-icon ' + product.category">{{ product.icon }}</mat-icon>
                      <mat-card-title>{{ product.name }}</mat-card-title>
                      <mat-card-subtitle>
                        <span class="status-badge" [class]="product.status.toLowerCase().replace(' ', '-')">
                          {{ product.status }}
                        </span>
                      </mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ product.description }}</p>
                      <ul class="feature-list">
                        @for (feature of product.features; track feature) {
                          <li>{{ feature }}</li>
                        }
                      </ul>
                      <div class="pricing">
                        <span class="price">{{ product.pricing }}</span>
                      </div>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">Learn More</button>
                      <button mat-stroked-button routerLink="/support">Contact Sales</button>
                    </mat-card-actions>
                  </mat-card>
                }
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Transform Your Business?</h2>
          <p>Explore our enterprise products and discover how they can accelerate your digital transformation journey.</p>
          <div class="cta-actions">
            <button mat-raised-button color="primary" routerLink="/support">Schedule Demo</button>
            <button mat-stroked-button routerLink="/solutions">View Solutions</button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .products-page {
      min-height: 100vh;
    }

    .hero-section {
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      padding: 120px 0 80px;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      line-height: 1.2;
    }

    .hero-subtitle {
      font-size: 1.25rem;
      margin-bottom: 3rem;
      opacity: 0.9;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: 4rem;
      margin-top: 3rem;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .products-section {
      padding: 80px 0;
    }

    .product-tabs {
      margin-bottom: 2rem;
    }

    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .product-card {
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .product-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .product-icon {
      font-size: 2rem;
      margin-right: 1rem;
    }

    .product-icon.business { color: var(--ai-blue); }
    .product-icon.cloud { color: var(--vision-cyan); }
    .product-icon.ai { color: var(--generative-purple); }

    .status-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .status-badge.available {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-badge.coming-soon {
      background: #fff3e0;
      color: #f57c00;
    }

    .status-badge.beta {
      background: #e3f2fd;
      color: #1976d2;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 1rem 0;
    }

    .feature-list li {
      padding: 0.25rem 0;
      position: relative;
      padding-left: 1.5rem;
    }

    .feature-list li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: var(--nlp-green);
      font-weight: bold;
    }

    .pricing {
      margin: 1rem 0;
      padding: 1rem;
      background: #f5f5f5;
      border-radius: 8px;
      text-align: center;
    }

    .price {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--primary-blue);
    }

    .cta-section {
      background: #f8f9fa;
      padding: 80px 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .cta-section p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      color: #666;
    }

    .cta-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2.5rem;
      }

      .hero-stats {
        flex-direction: column;
        gap: 2rem;
      }

      .products-grid {
        grid-template-columns: 1fr;
      }

      .cta-actions {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class ProductsComponent implements OnInit {
  businessProducts: Product[] = [
    {
      name: 'QuadERP Enterprise',
      description: 'Comprehensive enterprise resource planning solution with advanced analytics and AI-driven insights.',
      features: ['Financial Management', 'Supply Chain Optimization', 'HR & Payroll', 'Real-time Analytics'],
      icon: 'business',
      category: 'business',
      status: 'Available',
      pricing: 'Starting at $299/user/month'
    },
    {
      name: 'QuadCRM Pro',
      description: 'Advanced customer relationship management platform with intelligent automation and predictive analytics.',
      features: ['Lead Management', 'Sales Automation', 'Customer Analytics', 'Mobile Access'],
      icon: 'people',
      category: 'business',
      status: 'Available',
      pricing: 'Starting at $149/user/month'
    }
  ];

  cloudProducts: Product[] = [
    {
      name: 'QuadCloud Platform',
      description: 'Scalable cloud infrastructure platform with enterprise-grade security and compliance.',
      features: ['Auto-scaling', 'Multi-region Deployment', 'Advanced Security', '99.9% SLA'],
      icon: 'cloud',
      category: 'cloud',
      status: 'Available',
      pricing: 'Pay-as-you-scale'
    }
  ];

  aiProducts: Product[] = [
    {
      name: 'QuadAI Analytics',
      description: 'Advanced AI-powered analytics platform for business intelligence and predictive modeling.',
      features: ['Machine Learning Models', 'Predictive Analytics', 'Natural Language Processing', 'Real-time Insights'],
      icon: 'psychology',
      category: 'ai',
      status: 'Beta',
      pricing: 'Custom pricing'
    }
  ];

  constructor(private seoService: SEOService) {}

  ngOnInit() {
    this.seoService.updateSEO({
      title: 'Enterprise Software Products - Quadrate Technologies',
      description: 'Discover our comprehensive suite of enterprise software products designed to accelerate digital transformation and drive business innovation.',
      keywords: 'enterprise software, business applications, cloud platforms, AI analytics, ERP, CRM'
    });
  }
}
