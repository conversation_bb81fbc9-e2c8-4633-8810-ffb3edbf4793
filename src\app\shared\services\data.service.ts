import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, timer, of, forkJoin } from 'rxjs';
import { catchError, retry, shareReplay, tap, switchMap, map } from 'rxjs/operators';
import { NotificationService } from './notification.service';

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: number;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

export interface LoadingState {
  [key: string]: boolean;
}

export interface ErrorState {
  [key: string]: string | null;
}

export interface DataServiceConfig {
  baseUrl: string;
  defaultCacheDuration: number;
  retryAttempts: number;
  retryDelay: number;
  enableNotifications: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class DataService {
  private http = inject(HttpClient);
  private notificationService = inject(NotificationService);
  
  private cache = new Map<string, CacheEntry<any>>();
  private loadingState$ = new BehaviorSubject<LoadingState>({});
  private errorState$ = new BehaviorSubject<ErrorState>({});
  
  private config: DataServiceConfig = {
    baseUrl: '/api',
    defaultCacheDuration: 5 * 60 * 1000, // 5 minutes
    retryAttempts: 3,
    retryDelay: 1000,
    enableNotifications: true
  };

  constructor() {}

  // Configuration
  configure(config: Partial<DataServiceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // Loading state management
  getLoadingState(): Observable<LoadingState> {
    return this.loadingState$.asObservable();
  }

  isLoading(key: string): Observable<boolean> {
    return this.loadingState$.pipe(
      map(state => state[key] || false)
    );
  }

  private setLoading(key: string, loading: boolean): void {
    const currentState = this.loadingState$.value;
    this.loadingState$.next({
      ...currentState,
      [key]: loading
    });
  }

  // Error state management
  getErrorState(): Observable<ErrorState> {
    return this.errorState$.asObservable();
  }

  getError(key: string): Observable<string | null> {
    return this.errorState$.pipe(
      map(state => state[key] || null)
    );
  }

  private setError(key: string, error: string | null): void {
    const currentState = this.errorState$.value;
    this.errorState$.next({
      ...currentState,
      [key]: error
    });
  }

  // Cache management
  private getCacheKey(url: string, params?: any): string {
    const paramString = params ? JSON.stringify(params) : '';
    return `${url}${paramString}`;
  }

  private getCachedData<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  private setCachedData<T>(key: string, data: T, duration?: number): void {
    const cacheDuration = duration || this.config.defaultCacheDuration;
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + cacheDuration
    };
    this.cache.set(key, entry);
  }

  clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // HTTP methods with caching and error handling
  get<T>(
    endpoint: string, 
    options: {
      params?: any;
      useCache?: boolean;
      cacheDuration?: number;
      loadingKey?: string;
      showErrorNotification?: boolean;
    } = {}
  ): Observable<T> {
    const {
      params,
      useCache = true,
      cacheDuration,
      loadingKey = endpoint,
      showErrorNotification = this.config.enableNotifications
    } = options;

    const url = `${this.config.baseUrl}${endpoint}`;
    const cacheKey = this.getCacheKey(url, params);

    // Check cache first
    if (useCache) {
      const cachedData = this.getCachedData<T>(cacheKey);
      if (cachedData) {
        return of(cachedData);
      }
    }

    // Set loading state
    this.setLoading(loadingKey, true);
    this.setError(loadingKey, null);

    // Build HTTP params
    let httpParams = new HttpParams();
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          httpParams = httpParams.set(key, params[key].toString());
        }
      });
    }

    return this.http.get<ApiResponse<T>>(url, { params: httpParams }).pipe(
      retry({
        count: this.config.retryAttempts,
        delay: (error, retryCount) => {
          if (showErrorNotification && retryCount === this.config.retryAttempts) {
            this.notificationService.error(
              `Failed to load data after ${this.config.retryAttempts} attempts`,
              'Network Error'
            );
          }
          return timer(this.config.retryDelay * retryCount);
        }
      }),
      map(response => response.data),
      tap(data => {
        if (useCache) {
          this.setCachedData(cacheKey, data, cacheDuration);
        }
      }),
      catchError(error => this.handleError(error, loadingKey, showErrorNotification)),
      tap(() => this.setLoading(loadingKey, false)),
      shareReplay(1)
    );
  }

  post<T>(
    endpoint: string,
    data: any,
    options: {
      loadingKey?: string;
      showSuccessNotification?: boolean;
      showErrorNotification?: boolean;
      successMessage?: string;
    } = {}
  ): Observable<T> {
    const {
      loadingKey = endpoint,
      showSuccessNotification = this.config.enableNotifications,
      showErrorNotification = this.config.enableNotifications,
      successMessage = 'Operation completed successfully'
    } = options;

    const url = `${this.config.baseUrl}${endpoint}`;

    this.setLoading(loadingKey, true);
    this.setError(loadingKey, null);

    return this.http.post<ApiResponse<T>>(url, data).pipe(
      map(response => response.data),
      tap(() => {
        if (showSuccessNotification) {
          this.notificationService.success(successMessage);
        }
        // Clear related cache entries
        this.clearCache(endpoint);
      }),
      catchError(error => this.handleError(error, loadingKey, showErrorNotification)),
      tap(() => this.setLoading(loadingKey, false))
    );
  }

  put<T>(
    endpoint: string,
    data: any,
    options: {
      loadingKey?: string;
      showSuccessNotification?: boolean;
      showErrorNotification?: boolean;
      successMessage?: string;
    } = {}
  ): Observable<T> {
    const {
      loadingKey = endpoint,
      showSuccessNotification = this.config.enableNotifications,
      showErrorNotification = this.config.enableNotifications,
      successMessage = 'Update completed successfully'
    } = options;

    const url = `${this.config.baseUrl}${endpoint}`;

    this.setLoading(loadingKey, true);
    this.setError(loadingKey, null);

    return this.http.put<ApiResponse<T>>(url, data).pipe(
      map(response => response.data),
      tap(() => {
        if (showSuccessNotification) {
          this.notificationService.success(successMessage);
        }
        // Clear related cache entries
        this.clearCache(endpoint);
      }),
      catchError(error => this.handleError(error, loadingKey, showErrorNotification)),
      tap(() => this.setLoading(loadingKey, false))
    );
  }

  delete<T>(
    endpoint: string,
    options: {
      loadingKey?: string;
      showSuccessNotification?: boolean;
      showErrorNotification?: boolean;
      successMessage?: string;
    } = {}
  ): Observable<T> {
    const {
      loadingKey = endpoint,
      showSuccessNotification = this.config.enableNotifications,
      showErrorNotification = this.config.enableNotifications,
      successMessage = 'Item deleted successfully'
    } = options;

    const url = `${this.config.baseUrl}${endpoint}`;

    this.setLoading(loadingKey, true);
    this.setError(loadingKey, null);

    return this.http.delete<ApiResponse<T>>(url).pipe(
      map(response => response.data),
      tap(() => {
        if (showSuccessNotification) {
          this.notificationService.success(successMessage);
        }
        // Clear related cache entries
        this.clearCache(endpoint);
      }),
      catchError(error => this.handleError(error, loadingKey, showErrorNotification)),
      tap(() => this.setLoading(loadingKey, false))
    );
  }

  // Error handling
  private handleError(
    error: HttpErrorResponse,
    loadingKey: string,
    showNotification: boolean
  ): Observable<never> {
    let errorMessage = 'An unexpected error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Bad Request: Please check your input';
          break;
        case 401:
          errorMessage = 'Unauthorized: Please log in again';
          break;
        case 403:
          errorMessage = 'Forbidden: You do not have permission';
          break;
        case 404:
          errorMessage = 'Not Found: The requested resource was not found';
          break;
        case 500:
          errorMessage = 'Server Error: Please try again later';
          break;
        default:
          errorMessage = `Error ${error.status}: ${error.message}`;
      }

      if (error.error?.message) {
        errorMessage = error.error.message;
      }
    }

    this.setError(loadingKey, errorMessage);

    if (showNotification) {
      this.notificationService.error(errorMessage, 'Request Failed');
    }

    return throwError(() => new Error(errorMessage));
  }

  // Utility methods
  refreshData<T>(
    endpoint: string,
    params?: any,
    options?: {
      cacheDuration?: number;
      loadingKey?: string;
    }
  ): Observable<T> {
    const cacheKey = this.getCacheKey(`${this.config.baseUrl}${endpoint}`, params);
    this.cache.delete(cacheKey);
    return this.get<T>(endpoint, { ...options, params, useCache: true });
  }

  preloadData<T>(endpoint: string, params?: any): Observable<T> {
    return this.get<T>(endpoint, { params, showErrorNotification: false });
  }

  // Batch operations
  batchGet<T>(endpoints: string[]): Observable<T[]> {
    const requests = endpoints.map(endpoint => this.get<T>(endpoint));
    return forkJoin(requests);
  }
}
