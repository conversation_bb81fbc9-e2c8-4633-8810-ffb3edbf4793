# Firebase Deployment Guide

This guide will help you deploy your Angular application to Firebase Hosting.

## Prerequisites

✅ Firebase CLI is installed globally
✅ Project is built and ready for deployment
✅ Firebase configuration files are created

## Steps to Deploy

### 1. Authenticate with Firebase

Run the following command and follow the authentication process:

```bash
firebase login
```

This will open a browser window where you can sign in with your Google account.

### 2. Create or Select a Firebase Project

If you don't have a Firebase project yet:

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or select an existing project
3. Enable Firebase Hosting in the project

### 3. Update Project Configuration

Edit the `.firebaserc` file and replace `your-firebase-project-id` with your actual Firebase project ID:

```json
{
  "projects": {
    "default": "your-actual-project-id"
  }
}
```

### 4. Deploy to Firebase

Run one of these commands:

```bash
# Option 1: Use the custom deploy script
npm run deploy

# Option 2: Manual deployment
ng build --configuration production
firebase deploy

# Option 3: Deploy hosting only
firebase deploy --only hosting
```

### 5. Test Local Firebase Hosting (Optional)

To test your app locally with Firebase hosting:

```bash
npm run firebase:serve
```

Or manually:

```bash
ng build --configuration production
firebase serve
```

## Configuration Files Created

- `firebase.json` - Firebase project configuration
- `.firebaserc` - Firebase project aliases
- Updated `package.json` with deployment scripts

## Firebase Configuration Details

The `firebase.json` file is configured with:

- **Public Directory**: `dist/qts-angular/browser` (Angular build output)
- **SPA Rewrites**: All routes redirect to `index.html` for Angular routing
- **Caching Headers**: Optimized caching for static assets
- **CORS Headers**: Proper headers for font files

## Troubleshooting

### Authentication Issues

If you encounter authentication problems:

```bash
firebase logout
firebase login --reauth
```

### Build Issues

If the build fails, check:

1. All dependencies are installed: `npm install`
2. TypeScript compilation errors
3. Angular configuration in `angular.json`

### Deployment Issues

1. Verify your Firebase project ID in `.firebaserc`
2. Ensure Firebase Hosting is enabled in your project
3. Check that the build output directory exists: `dist/qts-angular/browser`

## Next Steps

After successful deployment:

1. Your app will be available at: `https://your-project-id.web.app`
2. You can set up custom domains in the Firebase Console
3. Configure CI/CD for automatic deployments
4. Set up Firebase Analytics, Performance Monitoring, etc.

## Useful Commands

```bash
# Check Firebase projects
firebase projects:list

# Check deployment status
firebase hosting:sites:list

# View deployment history
firebase hosting:releases:list

# Rollback to previous version
firebase hosting:rollback
```