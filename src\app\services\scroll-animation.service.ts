import { Injectable, ElementRef, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class ScrollAnimationService {
  private observer: IntersectionObserver | null = null;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.initializeObserver();
  }

  private initializeObserver() {
    if (isPlatformBrowser(this.platformId) && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('visible');
              // Add staggered animation for child elements
              const children = entry.target.querySelectorAll('.stagger-child');
              children.forEach((child, index) => {
                setTimeout(() => {
                  child.classList.add('visible');
                }, index * 100);
              });
            }
          });
        },
        {
          threshold: 0.1,
          rootMargin: '0px 0px -50px 0px'
        }
      );
    }
  }

  observeElement(element: ElementRef) {
    if (this.observer && element.nativeElement) {
      element.nativeElement.classList.add('fade-in');
      this.observer.observe(element.nativeElement);
    }
  }

  unobserveElement(element: ElementRef) {
    if (this.observer && element.nativeElement) {
      this.observer.unobserve(element.nativeElement);
    }
  }

  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}
