import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { SEOService } from '../../services/seo.service';

@Component({
  selector: 'app-support',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="support-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Enterprise Support Center</h1>
          <p class="hero-subtitle">
            Get expert support for your enterprise technology solutions. Our dedicated support team 
            provides 24/7 assistance, comprehensive documentation, and strategic guidance to ensure your success.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">24/7</span>
              <span class="stat-label">Support Coverage</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">&lt;2hrs</span>
              <span class="stat-label">Response Time</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">Resolution Rate</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Support Options -->
      <div class="support-options">
        <div class="container">
          <h2>Choose Your Support Channel</h2>
          <p class="section-subtitle">Multiple ways to get the help you need, when you need it</p>
          <div class="support-grid">
            <mat-card class="support-card">
              <mat-card-header>
                <mat-icon mat-card-avatar class="priority-icon">priority_high</mat-icon>
                <mat-card-title>Emergency Support</mat-card-title>
                <mat-card-subtitle>Critical issues affecting production</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="support-details">
                  <div class="response-time">
                    <strong>Response Time:</strong> &lt;15 minutes
                  </div>
                  <div class="availability">
                    <strong>Availability:</strong> 24/7/365
                  </div>
                  <div class="contact-methods">
                    <button mat-raised-button color="warn" class="contact-btn">
                      <mat-icon>phone</mat-icon>
                      Call Emergency Line
                    </button>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="support-card">
              <mat-card-header>
                <mat-icon mat-card-avatar class="standard-icon">support_agent</mat-icon>
                <mat-card-title>Standard Support</mat-card-title>
                <mat-card-subtitle>General inquiries and technical assistance</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="support-details">
                  <div class="response-time">
                    <strong>Response Time:</strong> &lt;2 hours
                  </div>
                  <div class="availability">
                    <strong>Availability:</strong> Business hours
                  </div>
                  <div class="contact-methods">
                    <button mat-raised-button color="primary" class="contact-btn">
                      <mat-icon>email</mat-icon>
                      Submit Ticket
                    </button>
                    <button mat-stroked-button class="contact-btn">
                      <mat-icon>chat</mat-icon>
                      Live Chat
                    </button>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="support-card">
              <mat-card-header>
                <mat-icon mat-card-avatar class="consultation-icon">psychology</mat-icon>
                <mat-card-title>Strategic Consultation</mat-card-title>
                <mat-card-subtitle>Architecture and planning guidance</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="support-details">
                  <div class="response-time">
                    <strong>Response Time:</strong> &lt;24 hours
                  </div>
                  <div class="availability">
                    <strong>Availability:</strong> Scheduled sessions
                  </div>
                  <div class="contact-methods">
                    <button mat-raised-button color="accent" class="contact-btn">
                      <mat-icon>calendar_today</mat-icon>
                      Schedule Consultation
                    </button>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>

      <!-- Contact Information -->
      <div class="contact-info">
        <div class="container">
          <h2>Get in Touch</h2>
          <div class="contact-grid">
            <div class="contact-method">
              <mat-icon>email</mat-icon>
              <div>
                <h4>Email Support</h4>
                <p>support&#64;quadratetech.com</p>
              </div>
            </div>
            <div class="contact-method">
              <mat-icon>phone</mat-icon>
              <div>
                <h4>Phone Support</h4>
                <p>+****************</p>
              </div>
            </div>
            <div class="contact-method">
              <mat-icon>location_on</mat-icon>
              <div>
                <h4>Global Offices</h4>
                <p>12 locations worldwide</p>
              </div>
            </div>
            <div class="contact-method">
              <mat-icon>schedule</mat-icon>
              <div>
                <h4>Business Hours</h4>
                <p>24/7 Emergency Support</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .support-page {
      min-height: 100vh;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .hero-subtitle {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: 3rem;
      margin-top: 3rem;
      flex-wrap: wrap;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .support-options {
      padding: 80px 0;
    }

    .support-options h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .section-subtitle {
      text-align: center;
      margin-bottom: 3rem;
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .support-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
    }

    .support-card {
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .support-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .priority-icon {
      background: #f44336 !important;
      color: white !important;
    }

    .standard-icon {
      background: #2196f3 !important;
      color: white !important;
    }

    .consultation-icon {
      background: #9c27b0 !important;
      color: white !important;
    }

    .contact-info {
      padding: 80px 0;
      background: #f8f9fa;
    }

    .contact-info h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 3rem;
      color: var(--primary-black);
    }

    .contact-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }

    .contact-method {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 2rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .contact-method mat-icon {
      font-size: 2.5rem;
      color: var(--primary-blue);
    }

    .contact-method h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-black);
    }

    .contact-method p {
      margin: 0;
      color: #666;
    }

    @media (max-width: 768px) {
      .hero-section {
        padding: 4rem 0;
      }

      .hero-section h1 {
        font-size: 2.8rem;
        line-height: 1.2;
      }

      .hero-subtitle {
        font-size: 1.1rem;
      }

      .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
      }

      .stat-item {
        min-width: auto;
      }

      .support-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .support-card {
        padding: 1.5rem;
      }

      .contact-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .contact-method {
        padding: 1.5rem;
      }

      .container {
        padding: 0 1rem;
      }

      .section-title {
        font-size: 2.2rem;
      }

      .section-subtitle {
        font-size: 1.1rem;
      }
    }

    @media (max-width: 480px) {
      .hero-section h1 {
        font-size: 2.2rem;
      }

      .hero-subtitle {
        font-size: 1rem;
      }

      .section-title {
        font-size: 1.8rem;
      }

      .support-card, .contact-method {
        padding: 1.2rem;
      }

      .contact-btn {
        font-size: 0.9rem;
        padding: 0.8rem 1.2rem;
      }
    }
  `]
})
export class SupportComponent implements OnInit {
  constructor(private seoService: SEOService) {}

  ngOnInit() {
    this.seoService.updateSEO({
      title: 'Enterprise Support Center - Quadrate Technologies',
      description: 'Get expert support for your enterprise technology solutions. 24/7 assistance, comprehensive documentation, and strategic guidance.',
      keywords: 'enterprise support, technical support, 24/7 support, customer service, technology assistance'
    });
  }
}
