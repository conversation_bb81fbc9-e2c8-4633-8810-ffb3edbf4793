import { Component, OnInit } from '@angular/core';
import { SEOService } from '../../services/seo.service';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterLink } from '@angular/router';

interface TeamMember {
  name: string;
  position: string;
  bio: string;
  avatar: string;
  expertise: string[];
  certifications: string[];
}

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, MatButtonModule, RouterLink],
  template: `
    <div class="about-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Leading Enterprise Technology Innovation</h1>
          <p class="hero-subtitle">
            Quadrate Technologies is a global leader in enterprise software solutions, serving Fortune 500 companies
            and emerging enterprises across 15+ industries. We combine deep technical expertise with strategic business
            insight to deliver transformative technology solutions that drive sustainable growth.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">500+</span>
              <span class="stat-label">Enterprise Clients</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">$50M+</span>
              <span class="stat-label">Cost Savings Delivered</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">15+</span>
              <span class="stat-label">Years of Excellence</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">Client Satisfaction</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Company Overview -->
      <div class="overview-section">
        <div class="container">
          <div class="overview-grid">
            <div class="overview-content">
              <h2>Our Vision</h2>
              <p class="vision-statement">
                To be the world's most trusted partner in enterprise digital transformation,
                empowering organizations to achieve unprecedented levels of innovation, efficiency, and growth.
              </p>
              <h3>Our Mission</h3>
              <p>
                We deliver cutting-edge enterprise technology solutions that transform how businesses operate,
                compete, and grow in the digital economy. Through our comprehensive suite of services—from
                cloud migration and AI implementation to custom software development—we enable organizations
                to unlock their full potential and achieve sustainable competitive advantage.
              </p>
              <div class="company-highlights">
                <div class="highlight-item">
                  <mat-icon>public</mat-icon>
                  <span>Global Presence</span>
                </div>
                <div class="highlight-item">
                  <mat-icon>verified</mat-icon>
                  <span>ISO 27001 Certified</span>
                </div>
                <div class="highlight-item">
                  <mat-icon>emoji_events</mat-icon>
                  <span>Industry Awards</span>
                </div>
              </div>
            </div>
            <div class="overview-image">
              <img src="https://ik.imagekit.io/quadrate/assets/img/hero-image.avif"
                   alt="Enterprise Technology Leadership"
                   loading="lazy">
            </div>
          </div>
        </div>
      </div>

      <!-- Leadership Team -->
      <div class="leadership-section">
        <div class="container">
          <h2>Executive Leadership</h2>
          <p class="section-subtitle">Meet the visionary leaders driving our mission to transform enterprise technology</p>
          <div class="leadership-grid">
            @for (leader of leadership; track leader.name) {
              <mat-card class="leader-card">
                <div class="leader-image">
                  <img [src]="leader.image" [alt]="leader.name" loading="lazy">
                </div>
                <mat-card-content>
                  <h3>{{ leader.name }}</h3>
                  <h4>{{ leader.title }}</h4>
                  <p>{{ leader.bio }}</p>
                  <div class="leader-expertise">
                    @for (skill of leader.expertise; track skill) {
                      <span class="expertise-tag">{{ skill }}</span>
                    }
                  </div>
                </mat-card-content>
              </mat-card>
            }
          </div>
        </div>
      </div>

      <!-- Core Values -->
      <div class="values-section">
        <div class="container">
          <h2>Our Core Values</h2>
          <p class="section-subtitle">The principles that guide our mission to deliver exceptional enterprise solutions</p>
          <div class="values-grid">
            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>rocket_launch</mat-icon>
                <mat-card-title>Innovation Excellence</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We pioneer cutting-edge technologies and methodologies to deliver breakthrough solutions that transform industries.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>security</mat-icon>
                <mat-card-title>Trust & Security</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We maintain the highest standards of security, compliance, and data protection to safeguard our clients' assets.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>trending_up</mat-icon>
                <mat-card-title>Measurable Impact</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We deliver solutions that drive quantifiable business outcomes and sustainable competitive advantage.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>handshake</mat-icon>
                <mat-card-title>Partnership</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We build long-term strategic partnerships with our clients, becoming an extension of their teams.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>public</mat-icon>
                <mat-card-title>Global Excellence</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We deliver world-class solutions with local expertise across multiple markets and time zones.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>eco</mat-icon>
                <mat-card-title>Sustainability</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We promote sustainable technology practices that benefit both business performance and environmental responsibility.</p>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>

      <!-- Global Presence -->
      <div class="global-section">
        <div class="container">
          <h2>Global Presence</h2>
          <p class="section-subtitle">Serving enterprise clients across continents with local expertise and global scale</p>
          <div class="global-stats">
            <div class="global-stat">
              <mat-icon>location_on</mat-icon>
              <h3>12</h3>
              <p>Global Offices</p>
            </div>
            <div class="global-stat">
              <mat-icon>language</mat-icon>
              <h3>25+</h3>
              <p>Countries Served</p>
            </div>
            <div class="global-stat">
              <mat-icon>schedule</mat-icon>
              <h3>24/7</h3>
              <p>Support Coverage</p>
            </div>
            <div class="global-stat">
              <mat-icon>groups</mat-icon>
              <h3>1000+</h3>
              <p>Expert Professionals</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Certifications & Awards -->
      <div class="certifications-section">
        <div class="container">
          <h2>Certifications & Recognition</h2>
          <p class="section-subtitle">Industry-leading certifications and awards that validate our expertise</p>
          <div class="certifications-grid">
            <div class="cert-item">
              <mat-icon>verified</mat-icon>
              <h4>ISO 27001</h4>
              <p>Information Security Management</p>
            </div>
            <div class="cert-item">
              <mat-icon>cloud_done</mat-icon>
              <h4>Microsoft Gold Partner</h4>
              <p>Azure & Office 365 Expertise</p>
            </div>
            <div class="cert-item">
              <mat-icon>star</mat-icon>
              <h4>AWS Advanced Partner</h4>
              <p>Cloud Infrastructure Excellence</p>
            </div>
            <div class="cert-item">
              <mat-icon>emoji_events</mat-icon>
              <h4>Industry Awards</h4>
              <p>Technology Innovation Recognition</p>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Transform Your Enterprise?</h2>
          <p>Partner with Quadrate Technologies to unlock your organization's full potential through innovative technology solutions.</p>
          <div class="cta-actions">
            <button mat-raised-button color="primary" routerLink="/support">Schedule Consultation</button>
            <button mat-stroked-button routerLink="/solutions">Explore Solutions</button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .about-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .hero-subtitle {
      font-size: 1.3rem;
      max-width: 800px;
      margin: 0 auto 3rem auto;
      opacity: 0.95;
      line-height: 1.6;
    }

    .hero-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      max-width: 800px;
      margin: 0 auto;
    }

    .stat-item {
      text-align: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
      color: #06B6D4;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .overview-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .overview-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      align-items: center;
    }

    .overview-content h2 {
      color: #0607E1;
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
    }

    .overview-content p {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 1rem;
      color: #555;
    }

    .overview-image img {
      width: 100%;
      height: auto;
      border-radius: 8px;
    }

    .values-section {
      padding: 4rem 0;
    }

    .values-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }

    .values-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 2rem;
    }

    .value-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .value-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .value-card mat-icon {
      color: #0607E1;
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .leadership-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .leadership-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }

    .leadership-content h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      margin-top: 2rem;
    }

    .leadership-content h3:first-child {
      margin-top: 0;
    }

    .leadership-content p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }

    .leadership-content ul {
      list-style-type: none;
      padding: 0;
    }

    .leadership-content li {
      padding: 0.5rem 0;
      padding-left: 1.5rem;
      position: relative;
      color: #555;
    }

    .leadership-content li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #0607E1;
      font-weight: bold;
    }

    .team-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .team-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
    }

    .team-subtitle {
      text-align: center;
      color: #666;
      font-size: 1.1rem;
      max-width: 800px;
      margin: 0 auto 4rem auto;
      line-height: 1.6;
    }

    .team-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .team-card {
      padding: 2rem !important;
      text-align: center;
      transition: all 0.3s ease !important;
      border-radius: 16px !important;
      background: white !important;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    }

    .team-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    }

    .member-avatar {
      width: 120px;
      height: 120px;
      margin: 0 auto 1.5rem auto;
      border-radius: 50%;
      overflow: hidden;
      border: 4px solid #0607E1;
    }

    .member-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .member-info h3 {
      color: #333;
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .member-info h4 {
      color: #0607E1;
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .member-bio {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      font-size: 0.95rem;
    }

    .member-expertise,
    .member-certifications {
      margin-bottom: 1rem;
      text-align: left;
    }

    .member-expertise h5,
    .member-certifications h5 {
      color: #333;
      font-size: 0.9rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .expertise-tags,
    .cert-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .expertise-tag {
      background: rgba(6, 7, 225, 0.1);
      color: #0607E1;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .cert-badge {
      background: rgba(16, 185, 129, 0.1);
      color: #10B981;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .partnerships-section {
      padding: 6rem 0;
    }

    .partnerships-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
    }

    .partnerships-subtitle {
      text-align: center;
      color: #666;
      font-size: 1.1rem;
      max-width: 800px;
      margin: 0 auto 4rem auto;
      line-height: 1.6;
    }

    .partnerships-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .partnership-card {
      padding: 3rem 2rem;
      background: white;
      border-radius: 16px;
      text-align: center;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .partnership-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      border-color: #0607E1;
    }

    .partnership-icon {
      margin-bottom: 1.5rem;
    }

    .partnership-icon img {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .partnership-card h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      font-size: 1.3rem;
      font-weight: 700;
    }

    .partnership-card p {
      color: #666;
      line-height: 1.6;
    }

    .cta-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .cta-buttons button {
      border-radius: 25px !important;
      padding: 0.75rem 2rem !important;
      font-weight: 600 !important;
    }

    .company-highlights {
      display: flex;
      gap: 2rem;
      margin-top: 2rem;
      flex-wrap: wrap;
    }

    .highlight-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      border-radius: 25px;
      font-weight: 600;
    }

    .highlight-item mat-icon {
      font-size: 1.25rem;
    }

    .leadership-section {
      padding: 80px 0;
      background: #f8f9fa;
    }

    .leadership-section h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .leadership-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .leader-card {
      text-align: center;
      transition: transform 0.3s ease;
    }

    .leader-card:hover {
      transform: translateY(-5px);
    }

    .leader-image {
      width: 150px;
      height: 150px;
      margin: 0 auto 1.5rem;
      border-radius: 50%;
      overflow: hidden;
      border: 4px solid var(--primary-blue);
    }

    .leader-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .leader-card h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--primary-black);
    }

    .leader-card h4 {
      font-size: 1rem;
      color: var(--primary-blue);
      margin-bottom: 1rem;
      font-weight: 500;
    }

    .leader-expertise {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      justify-content: center;
      margin-top: 1rem;
    }

    .expertise-tag {
      padding: 0.25rem 0.75rem;
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--generative-purple) 100%);
      color: white;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .global-section {
      padding: 80px 0;
    }

    .global-section h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .global-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .global-stat {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .global-stat mat-icon {
      font-size: 3rem;
      color: var(--primary-blue);
      margin-bottom: 1rem;
    }

    .global-stat h3 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: var(--primary-black);
    }

    .global-stat p {
      color: #666;
      font-weight: 500;
    }

    .certifications-section {
      padding: 80px 0;
      background: #f8f9fa;
    }

    .certifications-section h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .certifications-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .cert-item {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .cert-item:hover {
      transform: translateY(-5px);
    }

    .cert-item mat-icon {
      font-size: 3rem;
      color: var(--primary-blue);
      margin-bottom: 1rem;
    }

    .cert-item h4 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--primary-black);
    }

    .cert-item p {
      color: #666;
      line-height: 1.6;
    }

    @media (max-width: 768px) {
      .hero-section {
        padding: 4rem 0;
      }

      .hero-section h1 {
        font-size: 2.8rem;
        line-height: 1.2;
      }

      .hero-section p {
        font-size: 1.1rem;
      }

      .overview-grid {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
      }

      .values-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .value-card {
        padding: 1.5rem;
      }

      .partnerships-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .company-highlights {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
      }

      .leadership-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .leader-card {
        padding: 1.5rem;
      }

      .global-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }

      .certifications-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .cert-item {
        padding: 1.5rem;
      }

      .container {
        padding: 0 1rem;
      }

      .section-title {
        font-size: 2.2rem;
      }

      .section-subtitle {
        font-size: 1.1rem;
      }
    }

    @media (max-width: 480px) {
      .hero-section h1 {
        font-size: 2.2rem;
      }

      .hero-section p {
        font-size: 1rem;
      }

      .section-title {
        font-size: 1.8rem;
      }

      .value-card, .leader-card, .cert-item {
        padding: 1.2rem;
      }

      .global-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .stat-item {
        padding: 1rem;
      }
    }
  `]
})
export class AboutComponent implements OnInit {
  leadership = [
    {
      name: 'M.F.M Fazrin',
      title: 'Chief Executive Officer & Founder',
      bio: 'Visionary leader with 15+ years of experience in enterprise technology and digital transformation. Drives strategic vision and innovation across global operations.',
      image: 'https://via.placeholder.com/200x200/0607E1/FFFFFF?text=MF',
      expertise: ['Strategic Leadership', 'Digital Transformation', 'Enterprise Architecture']
    },
    {
      name: 'Sarah Chen',
      title: 'Chief Technology Officer',
      bio: 'Technology innovator with deep expertise in cloud architecture, AI/ML, and enterprise software development. Leads technical strategy and product development.',
      image: 'https://via.placeholder.com/200x200/4D0AFF/FFFFFF?text=SC',
      expertise: ['Cloud Architecture', 'AI/ML', 'Software Engineering']
    },
    {
      name: 'David Rodriguez',
      title: 'Chief Operating Officer',
      bio: 'Operations excellence leader focused on scaling global delivery capabilities and ensuring exceptional client outcomes across all engagements.',
      image: 'https://via.placeholder.com/200x200/06B6D4/FFFFFF?text=DR',
      expertise: ['Operations Management', 'Global Delivery', 'Process Optimization']
    },
    {
      name: 'Emily Johnson',
      title: 'Chief Security Officer',
      bio: 'Cybersecurity expert ensuring enterprise-grade security and compliance across all solutions and client engagements.',
      image: 'https://via.placeholder.com/200x200/EF4444/FFFFFF?text=EJ',
      expertise: ['Cybersecurity', 'Compliance', 'Risk Management']
    }
  ];

  constructor(private seoService: SEOService) {}

  ngOnInit() {
    this.seoService.updateSEO({
      title: 'About Quadrate Technologies - Enterprise Technology Leadership',
      description: 'Learn about Quadrate Technologies leadership, global presence, and commitment to delivering innovative enterprise technology solutions.',
      keywords: 'about quadrate technologies, enterprise technology company, technology leadership, global presence'
    });
  }
}
