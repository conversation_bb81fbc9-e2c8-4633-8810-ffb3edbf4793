import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
export type ButtonSize = 'sm' | 'md' | 'lg' | 'xl';

@Component({
  selector: 'qts-advanced-button',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule, MatProgressSpinnerModule],
  template: `
    <button
      [class]="getButtonClasses()"
      [disabled]="disabled || loading"
      (click)="handleClick($event)"
      [attr.aria-label]="ariaLabel"
      [attr.data-testid]="testId">
      
      <!-- Loading Spinner -->
      @if (loading) {
        <mat-spinner 
          [diameter]="getSpinnerSize()" 
          class="button-spinner">
        </mat-spinner>
      }
      
      <!-- Leading Icon -->
      @if (leadingIcon && !loading) {
        <mat-icon [class]="getIconClasses('leading')">{{ leadingIcon }}</mat-icon>
      }
      
      <!-- Button Content -->
      <span [class]="getContentClasses()">
        <ng-content></ng-content>
      </span>
      
      <!-- Trailing Icon -->
      @if (trailingIcon && !loading) {
        <mat-icon [class]="getIconClasses('trailing')">{{ trailingIcon }}</mat-icon>
      }
      
      <!-- Ripple Effect -->
      <div class="button-ripple" [class.active]="rippleActive"></div>
    </button>
  `,
  styles: [`
    :host {
      display: inline-block;
    }
    
    .qts-button {
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      border: none;
      border-radius: 0.5rem;
      font-family: 'Montserrat', sans-serif;
      font-weight: 600;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      user-select: none;
      outline: none;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
    
    .qts-button:focus-visible {
      outline: 2px solid #0607E1;
      outline-offset: 2px;
    }
    
    .qts-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }
    
    /* Variants */
    .qts-button--primary {
      background: var(--primary-gradient);
      color: white;
      border: 1px solid transparent;
    }

    .qts-button--primary:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(6, 7, 225, 0.4);
    }
    
    .qts-button--secondary {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      color: #333;
      border: 1px solid #dee2e6;
    }
    
    .qts-button--secondary:hover:not(:disabled) {
      background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .qts-button--outline {
      background: transparent;
      color: var(--primary-blue);
      border: 2px solid var(--primary-blue);
    }

    .qts-button--outline:hover:not(:disabled) {
      background: var(--primary-blue);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(6, 7, 225, 0.3);
    }

    .qts-button--ghost {
      background: transparent;
      color: var(--primary-blue);
      border: 1px solid transparent;
    }

    .qts-button--ghost:hover:not(:disabled) {
      background: rgba(6, 7, 225, 0.1);
      transform: translateY(-1px);
    }
    
    .qts-button--destructive {
      background: linear-gradient(135deg, var(--data-red) 0%, #DC2626 100%);
      color: white;
      border: 1px solid transparent;
    }
    
    .qts-button--destructive:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }
    
    /* Sizes */
    .qts-button--sm {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      min-height: 2rem;
    }
    
    .qts-button--md {
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      min-height: 2.5rem;
    }
    
    .qts-button--lg {
      padding: 1rem 2rem;
      font-size: 1.125rem;
      min-height: 3rem;
    }
    
    .qts-button--xl {
      padding: 1.25rem 2.5rem;
      font-size: 1.25rem;
      min-height: 3.5rem;
    }
    
    /* Full Width */
    .qts-button--full {
      width: 100%;
    }
    
    /* Icons */
    .button-icon {
      flex-shrink: 0;
    }
    
    .button-icon--sm {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
    
    .button-icon--md {
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
    }
    
    .button-icon--lg {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
    
    .button-icon--xl {
      font-size: 1.75rem;
      width: 1.75rem;
      height: 1.75rem;
    }
    
    /* Content */
    .button-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .button-content--loading {
      opacity: 0;
    }
    
    /* Spinner */
    .button-spinner {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    
    /* Ripple Effect */
    .button-ripple {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      transition: width 0.3s ease, height 0.3s ease;
      pointer-events: none;
    }
    
    .button-ripple.active {
      width: 200px;
      height: 200px;
    }
    
    /* Dark Mode Support */
    @media (prefers-color-scheme: dark) {
      .qts-button--secondary {
        background: linear-gradient(135deg, #374151 0%, #4B5563 100%);
        color: #F9FAFB;
        border-color: #6B7280;
      }
      
      .qts-button--ghost {
        color: var(--vision-cyan);
      }

      .qts-button--ghost:hover:not(:disabled) {
        background: rgba(6, 182, 212, 0.1);
      }
    }
    
    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
      .qts-button {
        transition: none;
      }
      
      .qts-button:hover:not(:disabled) {
        transform: none;
      }
      
      .button-ripple {
        display: none;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdvancedButtonComponent {
  @Input() variant: ButtonVariant = 'primary';
  @Input() size: ButtonSize = 'md';
  @Input() disabled = false;
  @Input() loading = false;
  @Input() fullWidth = false;
  @Input() leadingIcon?: string;
  @Input() trailingIcon?: string;
  @Input() ariaLabel?: string;
  @Input() testId?: string;
  
  @Output() clicked = new EventEmitter<Event>();
  
  rippleActive = false;
  
  getButtonClasses(): string {
    const classes = [
      'qts-button',
      `qts-button--${this.variant}`,
      `qts-button--${this.size}`
    ];
    
    if (this.fullWidth) {
      classes.push('qts-button--full');
    }
    
    return classes.join(' ');
  }
  
  getIconClasses(position: 'leading' | 'trailing'): string {
    return `button-icon button-icon--${this.size}`;
  }
  
  getContentClasses(): string {
    const classes = ['button-content'];
    
    if (this.loading) {
      classes.push('button-content--loading');
    }
    
    return classes.join(' ');
  }
  
  getSpinnerSize(): number {
    const sizes = {
      sm: 16,
      md: 20,
      lg: 24,
      xl: 28
    };
    return sizes[this.size];
  }
  
  handleClick(event: Event): void {
    if (this.disabled || this.loading) {
      event.preventDefault();
      return;
    }
    
    this.createRippleEffect();
    this.clicked.emit(event);
  }
  
  private createRippleEffect(): void {
    this.rippleActive = true;
    setTimeout(() => {
      this.rippleActive = false;
    }, 300);
  }
}
