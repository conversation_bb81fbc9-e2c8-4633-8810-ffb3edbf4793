/* Global styles for QTS Angular Application */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap');

/* Universal Box-Sizing Reset */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Prevent Horizontal Overflow */
html {
  overflow-x: hidden;
  max-width: 100vw;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* CSS Variables for Design System */
:root {
  /* Brand Colors */
  --primary-blue: #0607E1;
  --primary-white: #FFFFFF;
  --primary-black: #000000;

  /* Service Colors */
  --ai-blue: #0607E1;
  --generative-purple: #4D0AFF;
  --vision-cyan: #06B6D4;
  --nlp-green: #10B981;
  --dev-orange: #F59E0B;
  --data-red: #EF4444;
  --cloud-purple: #8B5CF6;

  /* Gradients */
  --primary-gradient: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
  --secondary-gradient: linear-gradient(135deg, #06B6D4 0%, #10B981 100%);
  --accent-gradient: linear-gradient(135deg, #F59E0B 0%, #EF4444 100%);

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);

  /* Animation Variables */
  --animation-fast: 0.2s ease;
  --animation-normal: 0.3s ease;
  --animation-slow: 0.5s ease;

  /* Dark Mode Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
}

/* Dark Mode Theme */
[data-theme="dark"] {
  --bg-primary: #0f0f0f;
  --bg-secondary: #1a1a1a;
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --border-color: #333333;

  /* Dark mode specific gradients */
  --primary-gradient: linear-gradient(135deg, #1a1aff 0%, #6d1aff 100%);
  --secondary-gradient: linear-gradient(135deg, #1aa6d4 0%, #1ab981 100%);
  --accent-gradient: linear-gradient(135deg, #ff9e0b 0%, #ff4444 100%);

  /* Dark mode shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.6);
}

/* Dark mode specific styles */
.dark-theme {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.dark-theme .mat-mdc-card {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.dark-theme .mat-mdc-raised-button {
  background: var(--primary-gradient) !important;
}

.dark-theme .mat-mdc-stroked-button {
  border-color: var(--primary-blue) !important;
  color: var(--primary-blue) !important;
}

.dark-theme .mat-mdc-stroked-button:hover {
  background: var(--primary-gradient) !important;
  color: white !important;
}

/* Global Styles */
body {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color var(--animation-normal), color var(--animation-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Responsive Images */
img {
  max-width: 100%;
  height: auto;
}

/* Prevent Text Overflow */
* {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Container Utility Classes */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
}

/* Prevent Horizontal Overflow on All Elements */
.no-overflow {
  overflow-x: hidden;
  max-width: 100%;
}

/* Global overflow prevention */
* {
  max-width: 100%;
}

/* Specific fixes for common overflow causes */
.mat-drawer-container,
.mat-sidenav-container,
router-outlet,
app-root {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Fix for Angular Material components */
.mat-toolbar {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Animation utilities */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--animation-slow), transform var(--animation-slow);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.scale-on-hover {
  transition: transform var(--animation-normal);
}

.scale-on-hover:hover {
  transform: scale(1.05);
}

/* Gradient text utility */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button enhancements */
.mat-mdc-raised-button {
  background: var(--primary-gradient) !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: all var(--animation-normal) !important;
  box-shadow: var(--shadow-md) !important;
}

.mat-mdc-raised-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.mat-mdc-stroked-button {
  border: 2px solid var(--primary-blue) !important;
  color: var(--primary-blue) !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: all var(--animation-normal) !important;
}

.mat-mdc-stroked-button:hover {
  background: var(--primary-gradient) !important;
  color: white !important;
  transform: translateY(-2px) !important;
}

/* Card enhancements */
.mat-mdc-card {
  border-radius: 12px !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--animation-normal) !important;
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
}

.mat-mdc-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .fade-in {
    transform: translateY(10px);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}
