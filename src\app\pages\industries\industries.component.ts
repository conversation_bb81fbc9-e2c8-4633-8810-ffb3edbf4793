import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { SEOService } from '../../services/seo.service';

interface Industry {
  name: string;
  description: string;
  challenges: string[];
  solutions: string[];
  icon: string;
  color: string;
  stats: {
    label: string;
    value: string;
  }[];
}

@Component({
  selector: 'app-industries',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, RouterLink],
  template: `
    <div class="industries-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Industry-Specific Solutions</h1>
          <p class="hero-subtitle">
            Delivering tailored technology solutions across diverse industries. 
            Our deep sector expertise ensures solutions that address your unique challenges and drive measurable results.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">15+</span>
              <span class="stat-label">Industries Served</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">500+</span>
              <span class="stat-label">Enterprise Clients</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">98%</span>
              <span class="stat-label">Client Satisfaction</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Industries Grid -->
      <div class="industries-section">
        <div class="container">
          <div class="industries-grid">
            @for (industry of industries; track industry.name) {
              <mat-card class="industry-card" [style.border-top-color]="industry.color">
                <mat-card-header>
                  <mat-icon [class]="'industry-icon'" [style.color]="industry.color">{{ industry.icon }}</mat-icon>
                  <mat-card-title>{{ industry.name }}</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <p class="industry-description">{{ industry.description }}</p>
                  
                  <div class="challenges-section">
                    <h4>Key Challenges</h4>
                    <ul class="challenge-list">
                      @for (challenge of industry.challenges; track challenge) {
                        <li>{{ challenge }}</li>
                      }
                    </ul>
                  </div>

                  <div class="solutions-section">
                    <h4>Our Solutions</h4>
                    <ul class="solution-list">
                      @for (solution of industry.solutions; track solution) {
                        <li>{{ solution }}</li>
                      }
                    </ul>
                  </div>

                  <div class="stats-section">
                    <div class="industry-stats">
                      @for (stat of industry.stats; track stat.label) {
                        <div class="stat-item">
                          <span class="stat-value">{{ stat.value }}</span>
                          <span class="stat-label">{{ stat.label }}</span>
                        </div>
                      }
                    </div>
                  </div>
                </mat-card-content>
                <mat-card-actions>
                  <button mat-raised-button [style.background-color]="industry.color" style="color: white;">
                    Learn More
                  </button>
                  <button mat-stroked-button routerLink="/case-studies">View Case Studies</button>
                </mat-card-actions>
              </mat-card>
            }
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Don't See Your Industry?</h2>
          <p>We work across many sectors and can adapt our solutions to meet your specific industry requirements.</p>
          <div class="cta-actions">
            <button mat-raised-button color="primary" routerLink="/support">Discuss Your Needs</button>
            <button mat-stroked-button routerLink="/solutions">Explore All Solutions</button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .industries-page {
      min-height: 100vh;
    }

    .hero-section {
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--vision-cyan) 100%);
      color: white;
      padding: 120px 0 80px;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      line-height: 1.2;
    }

    .hero-subtitle {
      font-size: 1.25rem;
      margin-bottom: 3rem;
      opacity: 0.9;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: 4rem;
      margin-top: 3rem;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .industries-section {
      padding: 80px 0;
    }

    .industries-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .industry-card {
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-top: 4px solid transparent;
    }

    .industry-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    }

    .industry-icon {
      font-size: 2.5rem;
      margin-right: 1rem;
    }

    .industry-description {
      margin-bottom: 1.5rem;
      color: #666;
      line-height: 1.6;
    }

    .challenges-section, .solutions-section {
      margin-bottom: 1.5rem;
    }

    .challenges-section h4, .solutions-section h4 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--primary-black);
    }

    .challenge-list, .solution-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .challenge-list li {
      padding: 0.25rem 0;
      position: relative;
      padding-left: 1.5rem;
      color: #666;
      font-size: 0.9rem;
    }

    .challenge-list li::before {
      content: '⚠';
      position: absolute;
      left: 0;
      color: #ff9800;
    }

    .solution-list li {
      padding: 0.25rem 0;
      position: relative;
      padding-left: 1.5rem;
      color: #666;
      font-size: 0.9rem;
    }

    .solution-list li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: var(--nlp-green);
      font-weight: bold;
    }

    .stats-section {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid #eee;
    }

    .industry-stats {
      display: flex;
      justify-content: space-around;
      text-align: center;
    }

    .industry-stats .stat-item {
      flex: 1;
    }

    .stat-value {
      display: block;
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--primary-blue);
    }

    .industry-stats .stat-label {
      font-size: 0.8rem;
      color: #666;
    }

    .cta-section {
      background: #f8f9fa;
      padding: 80px 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .cta-section p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      color: #666;
    }

    .cta-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2.5rem;
      }

      .hero-stats {
        flex-direction: column;
        gap: 2rem;
      }

      .industries-grid {
        grid-template-columns: 1fr;
      }

      .industry-stats {
        flex-direction: column;
        gap: 1rem;
      }

      .cta-actions {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class IndustriesComponent implements OnInit {
  industries: Industry[] = [
    {
      name: 'Healthcare & Life Sciences',
      description: 'Transforming patient care through innovative technology solutions that improve outcomes, reduce costs, and ensure compliance.',
      challenges: ['Data Security & HIPAA Compliance', 'Interoperability Issues', 'Legacy System Integration'],
      solutions: ['Electronic Health Records', 'Telemedicine Platforms', 'AI-Powered Diagnostics', 'Clinical Data Analytics'],
      icon: 'local_hospital',
      color: '#e91e63',
      stats: [
        { label: 'Hospitals Served', value: '50+' },
        { label: 'Patient Records', value: '2M+' },
        { label: 'Compliance Rate', value: '100%' }
      ]
    },
    {
      name: 'Financial Services',
      description: 'Enabling digital transformation in banking and finance with secure, scalable, and compliant solutions.',
      challenges: ['Regulatory Compliance', 'Cybersecurity Threats', 'Digital Transformation'],
      solutions: ['Core Banking Systems', 'Risk Management Platforms', 'Mobile Banking Apps', 'Blockchain Solutions'],
      icon: 'account_balance',
      color: '#2196f3',
      stats: [
        { label: 'Banks Served', value: '25+' },
        { label: 'Transactions/Day', value: '1M+' },
        { label: 'Security Score', value: '99.9%' }
      ]
    },
    {
      name: 'Manufacturing',
      description: 'Optimizing operations through Industry 4.0 solutions that enhance efficiency, quality, and sustainability.',
      challenges: ['Supply Chain Complexity', 'Equipment Downtime', 'Quality Control'],
      solutions: ['IoT Manufacturing Systems', 'Predictive Maintenance', 'Supply Chain Optimization', 'Quality Management'],
      icon: 'precision_manufacturing',
      color: '#ff9800',
      stats: [
        { label: 'Plants Connected', value: '100+' },
        { label: 'Downtime Reduced', value: '35%' },
        { label: 'Efficiency Gain', value: '28%' }
      ]
    },
    {
      name: 'Retail & E-commerce',
      description: 'Creating seamless omnichannel experiences that drive customer engagement and business growth.',
      challenges: ['Customer Experience', 'Inventory Management', 'Omnichannel Integration'],
      solutions: ['E-commerce Platforms', 'Customer Analytics', 'Inventory Management', 'Personalization Engines'],
      icon: 'shopping_cart',
      color: '#4caf50',
      stats: [
        { label: 'Retailers Served', value: '75+' },
        { label: 'Orders Processed', value: '10M+' },
        { label: 'Conversion Increase', value: '45%' }
      ]
    }
  ];

  constructor(private seoService: SEOService) {}

  ngOnInit() {
    this.seoService.updateSEO({
      title: 'Industry Solutions - Quadrate Technologies',
      description: 'Delivering tailored technology solutions across healthcare, finance, manufacturing, retail and more. Deep sector expertise for measurable results.',
      keywords: 'industry solutions, healthcare technology, fintech, manufacturing 4.0, retail technology, sector expertise'
    });
  }
}
