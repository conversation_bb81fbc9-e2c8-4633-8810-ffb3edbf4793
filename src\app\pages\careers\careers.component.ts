import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { RouterLink } from '@angular/router';

interface JobOpening {
  id: string;
  title: string;
  department: string;
  location: string;
  type: string;
  experience: string;
  description: string;
  requirements: string[];
  benefits: string[];
  skills: string[];
  salary?: string;
}

interface CompanyBenefit {
  title: string;
  description: string;
  icon: string;
}

@Component({
  selector: 'app-careers',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, RouterLink],
  template: `
    <div class="careers-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Join Our Mission to Engineer Business Momentum</h1>
          <p class="hero-subtitle">
            Be part of a team that's transforming how businesses leverage technology.
            At QTS, you'll work on cutting-edge projects that deliver real impact while growing your career.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">100%</span>
              <span class="stat-label">Remote-First Culture</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">40+</span>
              <span class="stat-label">Projects Delivered</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">95%</span>
              <span class="stat-label">Employee Satisfaction</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Why Work With Us Section -->
      <div class="why-us-section">
        <div class="container">
          <h2>Why Choose QTS as Your Career Destination?</h2>
          <p class="section-subtitle">
            We're not just building software - we're building careers, fostering innovation, and creating an environment where exceptional talent thrives.
          </p>

          <div class="benefits-grid">
            @for (benefit of companyBenefits; track benefit.title) {
              <div class="benefit-card">
                <mat-icon>{{ benefit.icon }}</mat-icon>
                <h3>{{ benefit.title }}</h3>
                <p>{{ benefit.description }}</p>
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Open Positions Section -->
      <div class="positions-section">
        <div class="container">
          <h2>Current Opportunities</h2>
          <p class="positions-subtitle">
            Join our growing team and help us deliver technology solutions that make a real difference.
          </p>

          <div class="positions-grid">
            @for (job of jobOpenings; track job.id) {
              <mat-card class="job-card">
                <div class="job-header">
                  <div class="job-title-section">
                    <h3>{{ job.title }}</h3>
                    <div class="job-meta">
                      <span class="department">{{ job.department }}</span>
                      <span class="location">{{ job.location }}</span>
                      <span class="type">{{ job.type }}</span>
                    </div>
                  </div>
                  @if (job.salary) {
                    <div class="salary-range">{{ job.salary }}</div>
                  }
                </div>

                <div class="job-content">
                  <p class="job-description">{{ job.description }}</p>

                  <div class="job-requirements">
                    <h4>Key Requirements:</h4>
                    <ul>
                      @for (req of job.requirements.slice(0, 3); track req) {
                        <li>{{ req }}</li>
                      }
                    </ul>
                  </div>

                  <div class="job-skills">
                    <h4>Skills:</h4>
                    <div class="skills-tags">
                      @for (skill of job.skills.slice(0, 4); track skill) {
                        <mat-chip class="skill-chip">{{ skill }}</mat-chip>
                      }
                      @if (job.skills.length > 4) {
                        <span class="more-skills">+{{ job.skills.length - 4 }} more</span>
                      }
                    </div>
                  </div>
                </div>

                <div class="job-actions">
                  <button mat-raised-button color="primary" routerLink="/contact">
                    Apply Now
                  </button>
                  <button mat-stroked-button>
                    Learn More
                  </button>
                </div>
              </mat-card>
            }
          </div>

          @if (jobOpenings.length === 0) {
            <div class="no-positions">
              <mat-icon>work_outline</mat-icon>
              <h3>No Current Openings</h3>
              <p>We're always looking for exceptional talent. Send us your resume and we'll keep you in mind for future opportunities.</p>
              <button mat-raised-button color="primary" routerLink="/contact">
                Submit Your Resume
              </button>
            </div>
          }
        </div>
      </div>

      <!-- Culture Section -->
      <div class="culture-section">
        <div class="container">
          <h2>Our Culture & Values</h2>
          <div class="culture-content">
            <div class="culture-text">
              <h3>Innovation-Driven Environment</h3>
              <p>
                We foster a culture of continuous learning and innovation. Our team members are encouraged to explore new technologies,
                contribute to open-source projects, and share knowledge across the organization.
              </p>

              <h3>Work-Life Balance</h3>
              <p>
                We believe that great work comes from well-rested, motivated individuals. Our flexible work arrangements and
                comprehensive benefits ensure you can perform at your best while maintaining a healthy work-life balance.
              </p>

              <h3>Growth & Development</h3>
              <p>
                Your career growth is our priority. We provide mentorship, training opportunities, conference attendance,
                and clear career progression paths to help you reach your professional goals.
              </p>
            </div>

            <div class="culture-highlights">
              <div class="highlight-item">
                <mat-icon>school</mat-icon>
                <h4>Continuous Learning</h4>
                <p>Annual learning budget for courses, certifications, and conferences</p>
              </div>
              <div class="highlight-item">
                <mat-icon>groups</mat-icon>
                <h4>Collaborative Team</h4>
                <p>Work with experts across different domains and technologies</p>
              </div>
              <div class="highlight-item">
                <mat-icon>trending_up</mat-icon>
                <h4>Career Growth</h4>
                <p>Clear advancement paths and mentorship programs</p>
              </div>
              <div class="highlight-item">
                <mat-icon>balance</mat-icon>
                <h4>Flexible Work</h4>
                <p>Remote-first culture with flexible hours and time off</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Make an Impact?</h2>
          <p>
            Join our team and help us build technology solutions that transform businesses and drive innovation.
            Your next career adventure starts here.
          </p>
          <div class="cta-buttons">
            <button mat-raised-button color="primary" routerLink="/contact">
              Get In Touch
            </button>
            <button mat-stroked-button routerLink="/about">
              Learn About Our Team
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .careers-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 5rem 0;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 70%, rgba(77, 10, 255, 0.3) 0%, transparent 50%),
                  radial-gradient(circle at 70% 30%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);
      pointer-events: none;
    }

    .hero-section h1 {
      font-size: 3.5rem;
      font-weight: 800;
      margin-bottom: 1.5rem;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 2;
    }

    .hero-subtitle {
      font-size: 1.3rem;
      max-width: 800px;
      margin: 0 auto 3rem auto;
      opacity: 0.95;
      line-height: 1.6;
      position: relative;
      z-index: 2;
    }

    .hero-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      max-width: 800px;
      margin: 0 auto;
      position: relative;
      z-index: 2;
    }

    .stat-item {
      text-align: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
      color: #06B6D4;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .why-us-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .why-us-section h2 {
      text-align: center;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .section-subtitle {
      text-align: center;
      color: #666;
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto 4rem auto;
      line-height: 1.6;
    }

    .benefits-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .benefit-card {
      text-align: center;
      padding: 2.5rem 2rem;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .benefit-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
      border-color: #0607E1;
    }

    .benefit-card mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #0607E1;
      margin-bottom: 1rem;
    }

    .benefit-card h3 {
      font-size: 1.3rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 1rem;
    }

    .benefit-card p {
      color: #666;
      line-height: 1.6;
    }

    .positions-section {
      padding: 6rem 0;
      background: #f8f9fa;
    }

    .positions-section h2 {
      text-align: center;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
      color: #333;
    }

    .positions-subtitle {
      text-align: center;
      color: #666;
      font-size: 1.1rem;
      max-width: 600px;
      margin: 0 auto 4rem auto;
      line-height: 1.6;
    }

    .positions-grid {
      display: grid;
      gap: 2rem;
    }

    .job-card {
      padding: 2rem !important;
      border-radius: 16px !important;
      background: white !important;
      transition: all 0.3s ease !important;
      border: 1px solid rgba(6, 7, 225, 0.1) !important;
    }

    .job-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
    }

    .job-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1.5rem;
    }

    .job-title-section h3 {
      font-size: 1.5rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 0.5rem;
    }

    .job-meta {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .job-meta span {
      background: rgba(6, 7, 225, 0.1);
      color: #0607E1;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .salary-range {
      background: linear-gradient(135deg, #10B981 0%, #06B6D4 100%);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .job-description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .job-requirements h4,
    .job-skills h4 {
      color: #333;
      font-weight: 600;
      margin-bottom: 0.75rem;
      font-size: 1rem;
    }

    .job-requirements ul {
      margin: 0 0 1.5rem 0;
      padding-left: 1.5rem;
    }

    .job-requirements li {
      color: #666;
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }

    .skills-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
    }

    .skill-chip {
      background: rgba(6, 7, 225, 0.1) !important;
      color: #0607E1 !important;
      font-size: 0.8rem !important;
    }

    .more-skills {
      color: #666;
      font-size: 0.8rem;
      align-self: center;
    }

    .job-actions {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .job-actions button {
      border-radius: 8px !important;
      font-weight: 600 !important;
    }

    .no-positions {
      text-align: center;
      padding: 4rem 2rem;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .no-positions mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #0607E1;
      margin-bottom: 1rem;
    }

    .no-positions h3 {
      color: #333;
      margin-bottom: 1rem;
    }

    .no-positions p {
      color: #666;
      margin-bottom: 2rem;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
    }

    .culture-section {
      padding: 6rem 0;
      background: white;
    }

    .culture-section h2 {
      text-align: center;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 3rem;
      color: #333;
    }

    .culture-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: start;
    }

    .culture-text h3 {
      color: #0607E1;
      font-weight: 700;
      margin-bottom: 1rem;
      margin-top: 2rem;
    }

    .culture-text h3:first-child {
      margin-top: 0;
    }

    .culture-text p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1rem;
    }

    .culture-highlights {
      display: grid;
      gap: 2rem;
    }

    .highlight-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 12px;
    }

    .highlight-item mat-icon {
      color: #0607E1;
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      margin-top: 0.25rem;
    }

    .highlight-item h4 {
      color: #333;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .highlight-item p {
      color: #666;
      font-size: 0.9rem;
      line-height: 1.5;
      margin: 0;
    }

    .cta-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .cta-buttons button {
      border-radius: 25px !important;
      padding: 0.75rem 2rem !important;
      font-weight: 600 !important;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2.5rem;
      }

      .hero-stats {
        grid-template-columns: 1fr;
      }

      .benefits-grid {
        grid-template-columns: 1fr;
      }

      .job-header {
        flex-direction: column;
        gap: 1rem;
      }

      .culture-content {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class CareersComponent {
  companyBenefits: CompanyBenefit[] = [
    {
      title: 'Competitive Compensation',
      description: 'Market-leading salaries, performance bonuses, and equity participation in company growth.',
      icon: 'payments'
    },
    {
      title: 'Health & Wellness',
      description: 'Comprehensive health insurance, mental health support, and wellness programs.',
      icon: 'favorite'
    },
    {
      title: 'Professional Development',
      description: 'Annual learning budget, conference attendance, and certification support.',
      icon: 'school'
    },
    {
      title: 'Flexible Work',
      description: 'Remote-first culture with flexible hours and unlimited PTO policy.',
      icon: 'schedule'
    },
    {
      title: 'Cutting-Edge Tech',
      description: 'Work with the latest technologies and tools in AI, cloud, and software development.',
      icon: 'computer'
    },
    {
      title: 'Innovation Time',
      description: '20% time for personal projects, open-source contributions, and skill development.',
      icon: 'lightbulb'
    }
  ];

  jobOpenings: JobOpening[] = [
    {
      id: '1',
      title: 'Senior Software Engineer',
      department: 'Engineering',
      location: 'Remote',
      type: 'Full-time',
      experience: '5+ years',
      salary: '$120k - $160k',
      description: 'Lead the development of custom software solutions and Microsoft 365 applications that drive business value for our clients. Work on cutting-edge projects involving web applications, system integration, and digital transformation.',
      requirements: [
        '5+ years of experience in software development',
        'Strong proficiency in modern programming languages (C#, JavaScript, Python)',
        'Experience with cloud platforms (AWS, Azure, GCP)',
        'Track record of deploying scalable applications in production',
        'Strong problem-solving and communication skills'
      ],
      benefits: ['Health Insurance', 'Equity', 'Learning Budget', 'Flexible PTO'],
      skills: ['C#', '.NET', 'JavaScript', 'React', 'Azure', 'Docker']
    },
    {
      id: '2',
      title: 'Full-Stack Developer',
      department: 'Engineering',
      location: 'Remote',
      type: 'Full-time',
      experience: '3+ years',
      salary: '$90k - $130k',
      description: 'Build scalable web applications and APIs that power our client solutions. Work across the full stack with modern technologies and frameworks.',
      requirements: [
        '3+ years of full-stack development experience',
        'Proficiency in React/Angular and Node.js',
        'Experience with databases (PostgreSQL, MongoDB)',
        'Knowledge of cloud services and DevOps practices',
        'Strong understanding of software engineering principles'
      ],
      benefits: ['Health Insurance', 'Equity', 'Learning Budget', 'Flexible PTO'],
      skills: ['React', 'Node.js', 'TypeScript', 'PostgreSQL', 'AWS', 'Docker']
    }
  ];
}
