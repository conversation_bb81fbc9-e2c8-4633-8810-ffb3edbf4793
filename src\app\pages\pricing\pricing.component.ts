import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterLink } from '@angular/router';

interface PricingPlan {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  highlighted: boolean;
  buttonText: string;
  popular?: boolean;
}

@Component({
  selector: 'app-pricing',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatTabsModule, RouterLink],
  template: `
    <div class="pricing-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Enterprise Solutions Pricing</h1>
          <p class="hero-subtitle">
            Flexible pricing models designed for enterprise scale. From fixed-price packages to
            custom solutions, we provide transparent pricing that scales with your business growth.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">500+</span>
              <span class="stat-label">Enterprise Clients</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">$50M+</span>
              <span class="stat-label">Value Delivered</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">ROI 300%</span>
              <span class="stat-label">Average Return</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing Models -->
      <div class="pricing-section">
        <div class="container">
          <h2>Choose Your Engagement Model</h2>
          <p class="section-subtitle">Flexible pricing approaches to meet your specific business requirements and budget constraints</p>

          <mat-tab-group class="pricing-tabs" animationDuration="300ms">
            <mat-tab label="Support Packages">
              <div class="tab-content">
                <div class="pricing-grid">
                  @for (plan of supportPlans; track plan.name) {
                    <mat-card class="pricing-card" [class.highlighted]="plan.highlighted" [class.popular]="plan.popular">
                      @if (plan.popular) {
                        <div class="popular-badge">Most Popular</div>
                      }
                      <mat-card-header>
                        <mat-card-title>{{ plan.name }}</mat-card-title>
                        <mat-card-subtitle>{{ plan.description }}</mat-card-subtitle>
                      </mat-card-header>
                      <mat-card-content>
                        <div class="price-section">
                          <span class="price">{{ plan.price }}</span>
                          <span class="period">{{ plan.period }}</span>
                        </div>
                        <ul class="features-list">
                          @for (feature of plan.features; track feature) {
                            <li>
                              <mat-icon>check</mat-icon>
                              <span>{{ feature }}</span>
                            </li>
                          }
                        </ul>
                      </mat-card-content>
                      <mat-card-actions>
                        <button mat-raised-button
                                [color]="plan.highlighted ? 'primary' : 'accent'"
                                class="plan-button"
                                routerLink="/contact">
                          {{ plan.buttonText }}
                        </button>
                      </mat-card-actions>
                    </mat-card>
                  }
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Solution Packages">
              <div class="tab-content">
                <div class="pricing-grid">
                  @for (plan of solutionPackages; track plan.name) {
                    <mat-card class="pricing-card" [class.highlighted]="plan.highlighted" [class.popular]="plan.popular">
                      @if (plan.popular) {
                        <div class="popular-badge">Most Popular</div>
                      }
                      <mat-card-header>
                        <mat-card-title>{{ plan.name }}</mat-card-title>
                        <mat-card-subtitle>{{ plan.description }}</mat-card-subtitle>
                      </mat-card-header>
                      <mat-card-content>
                        <div class="price-section">
                          <span class="price">{{ plan.price }}</span>
                          <span class="period">{{ plan.period }}</span>
                        </div>
                        <ul class="features-list">
                          @for (feature of plan.features; track feature) {
                            <li>
                              <mat-icon>check</mat-icon>
                              <span>{{ feature }}</span>
                            </li>
                          }
                        </ul>
                      </mat-card-content>
                      <mat-card-actions>
                        <button mat-raised-button
                                [color]="plan.highlighted ? 'primary' : 'accent'"
                                class="plan-button"
                                routerLink="/contact">
                          {{ plan.buttonText }}
                        </button>
                      </mat-card-actions>
                    </mat-card>
                  }
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Consulting Services">
              <div class="tab-content">
                <div class="pricing-grid">
                  @for (plan of consultingPlans; track plan.name) {
                    <mat-card class="pricing-card" [class.highlighted]="plan.highlighted">
                      <mat-card-header>
                        <mat-card-title>{{ plan.name }}</mat-card-title>
                        <mat-card-subtitle>{{ plan.description }}</mat-card-subtitle>
                      </mat-card-header>
                      <mat-card-content>
                        <div class="price-section">
                          <span class="price">{{ plan.price }}</span>
                          <span class="period">{{ plan.period }}</span>
                        </div>
                        <ul class="features-list">
                          @for (feature of plan.features; track feature) {
                            <li>
                              <mat-icon>check</mat-icon>
                              <span>{{ feature }}</span>
                            </li>
                          }
                        </ul>
                      </mat-card-content>
                      <mat-card-actions>
                        <button mat-raised-button
                                [color]="plan.highlighted ? 'primary' : 'accent'"
                                class="plan-button"
                                routerLink="/contact">
                          {{ plan.buttonText }}
                        </button>
                      </mat-card-actions>
                    </mat-card>
                  }
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="faq-section">
        <div class="container">
          <h2>Frequently Asked Questions</h2>
          <div class="faq-grid">
            <div class="faq-item">
              <h3>How do you determine project pricing?</h3>
              <p>Our pricing is based on project complexity, timeline, and resource requirements. We provide detailed estimates after understanding your specific needs.</p>
            </div>
            <div class="faq-item">
              <h3>Do you offer custom packages?</h3>
              <p>Yes, we create tailored solutions for unique requirements. Contact us to discuss your specific needs and get a custom quote.</p>
            </div>
            <div class="faq-item">
              <h3>What's included in ongoing support?</h3>
              <p>Our support includes bug fixes, minor updates, performance monitoring, and technical assistance during business hours.</p>
            </div>
            <div class="faq-item">
              <h3>Can I upgrade my plan later?</h3>
              <p>Absolutely! You can upgrade your service level at any time. We'll work with you to ensure a smooth transition.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Get Started?</h2>
          <p>Contact us today for a free consultation and custom quote for your project.</p>
          <div class="cta-buttons">
            <button mat-raised-button color="primary" routerLink="/contact">
              Get Free Consultation
            </button>
            <button mat-stroked-button routerLink="/services">
              View Our Services
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .pricing-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .hero-subtitle {
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto 2rem auto;
      opacity: 0.9;
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: 3rem;
      margin-top: 3rem;
      flex-wrap: wrap;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .pricing-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .pricing-section h2 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .section-subtitle {
      text-align: center;
      margin-bottom: 3rem;
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .pricing-tabs {
      margin-top: 2rem;
    }

    .tab-content {
      padding: 2rem 0;
    }

    .pricing-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .pricing-card {
      position: relative;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .pricing-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .pricing-card.highlighted {
      border: 2px solid #0607E1;
      transform: scale(1.05);
    }

    .pricing-card.popular {
      border: 2px solid #4D0AFF;
    }

    .popular-badge {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      background: #4D0AFF;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: bold;
    }

    .price-section {
      text-align: center;
      margin: 1.5rem 0;
    }

    .price {
      font-size: 2.5rem;
      font-weight: bold;
      color: #0607E1;
    }

    .period {
      font-size: 1rem;
      color: #666;
      margin-left: 0.5rem;
    }

    .features-list {
      list-style: none;
      padding: 0;
      margin: 1.5rem 0;
    }

    .features-list li {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
      color: #555;
    }

    .features-list mat-icon {
      color: #10B981;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    .plan-button {
      width: 100%;
      margin-top: auto;
    }

    .faq-section {
      padding: 4rem 0;
    }

    .faq-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }

    .faq-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .faq-item {
      padding: 1.5rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .faq-item h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }

    .faq-item p {
      color: #666;
      line-height: 1.6;
      margin: 0;
    }

    .cta-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2rem;
      }

      .hero-stats {
        flex-direction: column;
        gap: 2rem;
      }

      .pricing-grid {
        grid-template-columns: 1fr;
      }

      .pricing-card.highlighted {
        transform: none;
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class PricingComponent {
  supportPlans: PricingPlan[] = [
    {
      name: 'Essential Support',
      price: '$299',
      period: 'per month',
      description: 'Basic support for growing businesses with standard SLA',
      features: [
        'Business hours support (9-5 EST)',
        'Email and chat support',
        'Knowledge base access',
        'Community forums',
        'Basic documentation',
        '48-hour response time'
      ],
      highlighted: false,
      buttonText: 'Get Started'
    },
    {
      name: 'Professional Support',
      price: '$799',
      period: 'per month',
      description: 'Priority support for established enterprises with enhanced SLA',
      features: [
        '24/7 priority support',
        'Phone, email, and chat support',
        'Dedicated support manager',
        'Monthly health checks',
        'Training sessions included',
        '2-hour response time',
        'Custom integrations support'
      ],
      highlighted: true,
      popular: true,
      buttonText: 'Most Popular'
    },
    {
      name: 'Enterprise Support',
      price: 'Custom',
      period: 'pricing',
      description: 'Premium support for mission-critical operations with custom SLA',
      features: [
        '24/7 premium support',
        'Dedicated support team',
        'On-site support available',
        'Custom SLA agreements',
        'Strategic consulting included',
        '15-minute response time',
        'Proactive monitoring'
      ],
      highlighted: false,
      buttonText: 'Contact Sales'
    }
  ];

  solutionPackages: PricingPlan[] = [
    {
      name: 'Digital Foundation',
      price: '$25,000',
      period: 'starting at',
      description: 'Essential digital transformation package for growing enterprises',
      features: [
        'Cloud migration strategy',
        'Basic automation setup',
        'Security assessment',
        'Staff training (2 sessions)',
        '3-month implementation',
        '6-month support included'
      ],
      highlighted: false,
      buttonText: 'Get Started'
    },
    {
      name: 'Enterprise Accelerator',
      price: '$75,000',
      period: 'starting at',
      description: 'Comprehensive transformation package for established enterprises',
      features: [
        'Full cloud migration',
        'Advanced automation',
        'AI/ML implementation',
        'Custom integrations',
        '6-month implementation',
        '12-month support included',
        'Dedicated project manager'
      ],
      highlighted: true,
      popular: true,
      buttonText: 'Most Popular'
    },
    {
      name: 'Enterprise Transformation',
      price: 'Custom',
      period: 'quote',
      description: 'Complete digital transformation for large enterprises',
      features: [
        'Enterprise architecture design',
        'Multi-system integration',
        'Advanced security implementation',
        'Performance optimization',
        'Change management',
        '18-month implementation',
        '24-month support included',
        'Executive steering committee'
      ],
      highlighted: false,
      buttonText: 'Contact Sales'
    }
  ];

  consultingPlans: PricingPlan[] = [
    {
      name: 'Strategic Assessment',
      price: '$2,500',
      period: 'per engagement',
      description: 'Comprehensive technology and business alignment assessment',
      features: [
        '1-week comprehensive assessment',
        'Technology stack evaluation',
        'Digital maturity analysis',
        'Strategic roadmap development',
        'Executive presentation',
        '30-day follow-up support'
      ],
      highlighted: false,
      buttonText: 'Book Session'
    },
    {
      name: 'Monthly Retainer',
      price: '$2,500',
      period: 'per month',
      description: 'Ongoing technology consulting and support',
      features: [
        '10 hours monthly consulting',
        'Strategic planning',
        'Technology reviews',
        'Team mentoring',
        'Priority email support',
        'Monthly reports'
      ],
      highlighted: true,
      buttonText: 'Start Retainer'
    },
    {
      name: 'Transformation Program',
      price: 'Custom',
      period: 'quote',
      description: 'Complete digital transformation consulting',
      features: [
        'Comprehensive assessment',
        'Digital strategy development',
        'Implementation roadmap',
        'Change management',
        'Team training',
        'Ongoing support',
        'Success metrics tracking'
      ],
      highlighted: false,
      buttonText: 'Contact Sales'
    }
  ];
}
