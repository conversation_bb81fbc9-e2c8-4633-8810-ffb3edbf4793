import { <PERSON><PERSON>nent, OnInit, <PERSON><PERSON><PERSON>roy, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  content: string;
  rating: number;
  avatar: string;
  companyLogo: string;
  industry: string;
}

interface ClientLogo {
  name: string;
  logo: string;
  website: string;
}

@Component({
  selector: 'app-testimonials',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, MatButtonModule],
  template: `
    <div class="testimonials-section" #testimonialsSection>
      <div class="container">
        <!-- Section Header -->
        <div class="section-header fade-in">
          <h2 class="section-title">What Our Clients Say</h2>
          <p class="section-subtitle">
            Trusted by leading companies worldwide to deliver exceptional technology solutions
          </p>
        </div>
        
        <!-- Client Logos -->
        <div class="client-logos fade-in">
          <h3 class="logos-title">Trusted by Industry Leaders</h3>
          <div class="logos-grid">
            @for (logo of clientLogos; track logo.name; let i = $index) {
              <div 
                class="logo-item stagger-child"
                [style.animation-delay]="(i * 0.1) + 's'"
                [title]="logo.name">
                <img [src]="logo.logo" [alt]="logo.name + ' logo'" loading="lazy">
              </div>
            }
          </div>
        </div>
        
        <!-- Testimonials Grid -->
        <div class="testimonials-grid fade-in">
          @for (testimonial of visibleTestimonials; track testimonial.id; let i = $index) {
            <mat-card 
              class="testimonial-card stagger-child"
              [style.animation-delay]="(i * 0.2) + 's'">
              
              <!-- Quote Icon -->
              <div class="quote-icon">
                <mat-icon>format_quote</mat-icon>
              </div>
              
              <!-- Rating -->
              <div class="rating">
                @for (star of getStarArray(testimonial.rating); track $index) {
                  <mat-icon class="star">star</mat-icon>
                }
              </div>
              
              <!-- Testimonial Content -->
              <div class="testimonial-content">
                <p>"{{ testimonial.content }}"</p>
              </div>
              
              <!-- Client Info -->
              <div class="client-info">
                <div class="client-avatar">
                  <img [src]="testimonial.avatar" [alt]="testimonial.name" loading="lazy">
                </div>
                <div class="client-details">
                  <h4 class="client-name">{{ testimonial.name }}</h4>
                  <p class="client-position">{{ testimonial.position }}</p>
                  <div class="company-info">
                    <img [src]="testimonial.companyLogo" [alt]="testimonial.company" class="company-logo">
                    <span class="company-name">{{ testimonial.company }}</span>
                  </div>
                  <span class="industry-tag">{{ testimonial.industry }}</span>
                </div>
              </div>
            </mat-card>
          }
        </div>
        
        <!-- Load More Button -->
        @if (testimonials.length > visibleCount) {
          <div class="load-more-section">
            <button 
              mat-raised-button 
              color="primary" 
              class="load-more-btn"
              (click)="loadMoreTestimonials()">
              Load More Testimonials
              <mat-icon>expand_more</mat-icon>
            </button>
          </div>
        }
        
        <!-- CTA Section -->
        <div class="testimonials-cta fade-in">
          <h3>Ready to Join Our Success Stories?</h3>
          <p>Let's discuss how we can help transform your business with our proven solutions.</p>
          <button mat-raised-button color="primary" class="cta-button">
            Start Your Project
            <mat-icon>arrow_forward</mat-icon>
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .testimonials-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f0f8ff 100%);
      position: relative;
      overflow: hidden;
    }
    
    .testimonials-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 10% 20%, rgba(6, 7, 225, 0.05) 0%, transparent 50%),
                  radial-gradient(circle at 90% 80%, rgba(77, 10, 255, 0.05) 0%, transparent 50%);
      pointer-events: none;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      position: relative;
      z-index: 2;
    }
    
    .section-header {
      text-align: center;
      margin-bottom: 4rem;
    }
    
    .section-title {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .section-subtitle {
      font-size: 1.2rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .client-logos {
      margin-bottom: 5rem;
      text-align: center;
    }
    
    .logos-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 2rem;
    }
    
    .logos-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 2rem;
      align-items: center;
      justify-items: center;
    }
    
    .logo-item {
      opacity: 0.7;
      transition: all 0.3s ease;
      cursor: pointer;
      padding: 1rem;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .logo-item:hover {
      opacity: 1;
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .logo-item img {
      max-width: 120px;
      max-height: 60px;
      width: auto;
      height: auto;
      filter: grayscale(100%);
      transition: filter 0.3s ease;
    }
    
    .logo-item:hover img {
      filter: grayscale(0%);
    }
    
    .testimonials-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-bottom: 4rem;
    }
    
    .testimonial-card {
      position: relative;
      padding: 2rem !important;
      border-radius: 16px !important;
      background: rgba(255, 255, 255, 0.9) !important;
      backdrop-filter: blur(10px) !important;
      border: 1px solid rgba(0, 0, 0, 0.1) !important;
      transition: all 0.3s ease !important;
    }
    
    .testimonial-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    }
    
    .quote-icon {
      position: absolute;
      top: -10px;
      right: 20px;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(6, 7, 225, 0.3);
    }
    
    .rating {
      display: flex;
      gap: 0.25rem;
      margin-bottom: 1.5rem;
    }
    
    .star {
      color: #FFD700;
      font-size: 1.2rem !important;
      width: 1.2rem !important;
      height: 1.2rem !important;
    }
    
    .testimonial-content {
      margin-bottom: 2rem;
    }
    
    .testimonial-content p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: #333;
      font-style: italic;
      margin: 0;
    }
    
    .client-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .client-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      border: 3px solid #0607E1;
    }
    
    .client-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .client-details {
      flex: 1;
    }
    
    .client-name {
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.25rem 0;
    }
    
    .client-position {
      font-size: 0.9rem;
      color: #666;
      margin: 0 0 0.5rem 0;
    }
    
    .company-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
    }
    
    .company-logo {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }
    
    .company-name {
      font-weight: 500;
      color: #333;
    }
    
    .industry-tag {
      background: rgba(6, 7, 225, 0.1);
      color: #0607E1;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }
    
    .load-more-section {
      text-align: center;
      margin-bottom: 4rem;
    }
    
    .load-more-btn {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%) !important;
      color: white !important;
      border-radius: 25px !important;
      padding: 0.75rem 2rem !important;
      font-weight: 600 !important;
    }
    
    .testimonials-cta {
      text-align: center;
      padding: 3rem 2rem;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(6, 7, 225, 0.3);
    }
    
    .testimonials-cta h3 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }
    
    .testimonials-cta p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }
    
    .cta-button {
      background: rgba(255, 255, 255, 0.2) !important;
      color: white !important;
      border: 2px solid rgba(255, 255, 255, 0.3) !important;
      border-radius: 25px !important;
      padding: 0.75rem 2rem !important;
      font-weight: 600 !important;
      backdrop-filter: blur(10px) !important;
    }
    
    .cta-button:hover {
      background: rgba(255, 255, 255, 0.3) !important;
      transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
      .testimonials-section {
        padding: 4rem 0;
      }
      
      .section-title {
        font-size: 2rem;
      }
      
      .logos-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }
      
      .testimonials-grid {
        grid-template-columns: 1fr;
      }
      
      .testimonials-cta h3 {
        font-size: 1.5rem;
      }
    }
  `]
})
export class TestimonialsComponent implements OnInit {
  @ViewChild('testimonialsSection') testimonialsSection!: ElementRef;
  
  visibleCount = 3;
  
  testimonials: Testimonial[] = [
    {
      id: '1',
      name: 'Sarah Johnson',
      position: 'CTO',
      company: 'TechCorp Solutions',
      content: 'QTS transformed our data processing capabilities with their AI solutions. We saw a 40% increase in efficiency within the first quarter of implementation.',
      rating: 5,
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      companyLogo: 'https://via.placeholder.com/100x50/0607E1/FFFFFF?text=TechCorp',
      industry: 'Technology'
    },
    {
      id: '2',
      name: 'Michael Chen',
      position: 'Operations Director',
      company: 'Global Manufacturing Inc.',
      content: 'The custom development team at QTS delivered exactly what we needed. Their attention to detail and technical expertise exceeded our expectations.',
      rating: 5,
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      companyLogo: 'https://via.placeholder.com/100x50/4D0AFF/FFFFFF?text=GMI',
      industry: 'Manufacturing'
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      position: 'Head of Digital Innovation',
      company: 'FinanceFirst Bank',
      content: 'QTS helped us migrate to the cloud seamlessly. Their expertise in cloud solutions saved us both time and money while improving our system reliability.',
      rating: 5,
      avatar: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      companyLogo: 'https://via.placeholder.com/100x50/06B6D4/FFFFFF?text=FFB',
      industry: 'Finance'
    },
    {
      id: '4',
      name: 'David Thompson',
      position: 'VP of Technology',
      company: 'HealthTech Innovations',
      content: 'The AI-powered analytics platform QTS built for us has revolutionized how we handle patient data and improved our diagnostic accuracy significantly.',
      rating: 5,
      avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      companyLogo: 'https://via.placeholder.com/100x50/10B981/FFFFFF?text=HTI',
      industry: 'Healthcare'
    }
  ];
  
  clientLogos: ClientLogo[] = [
    { name: 'Microsoft', logo: 'https://via.placeholder.com/120x60/0078D4/FFFFFF?text=Microsoft', website: 'https://microsoft.com' },
    { name: 'ZOHO', logo: 'https://via.placeholder.com/120x60/FF6B35/FFFFFF?text=ZOHO', website: 'https://zoho.com' },
    { name: 'SAP', logo: 'https://via.placeholder.com/120x60/0FAAFF/FFFFFF?text=SAP', website: 'https://sap.com' },
    { name: 'AWS', logo: 'https://via.placeholder.com/120x60/FF9900/FFFFFF?text=AWS', website: 'https://aws.amazon.com' },
    { name: 'Google Cloud', logo: 'https://via.placeholder.com/120x60/4285F4/FFFFFF?text=Google', website: 'https://cloud.google.com' },
    { name: 'Azure', logo: 'https://via.placeholder.com/120x60/0078D4/FFFFFF?text=Azure', website: 'https://azure.microsoft.com' }
  ];
  
  get visibleTestimonials(): Testimonial[] {
    return this.testimonials.slice(0, this.visibleCount);
  }
  
  ngOnInit() {
    // Component initialization
  }
  
  getStarArray(rating: number): number[] {
    return Array(rating).fill(0);
  }
  
  loadMoreTestimonials() {
    this.visibleCount = Math.min(this.visibleCount + 2, this.testimonials.length);
  }
}
