import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

export type SkeletonVariant = 'text' | 'circular' | 'rectangular' | 'rounded';
export type SkeletonAnimation = 'pulse' | 'wave' | 'none';

@Component({
  selector: 'qts-skeleton-loader',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      [class]="getSkeletonClasses()"
      [style.width]="width"
      [style.height]="height"
      [style.border-radius]="getBorderRadius()"
      [attr.aria-label]="ariaLabel || 'Loading content'">
      
      @if (animation === 'wave') {
        <div class="skeleton-wave"></div>
      }
    </div>
  `,
  styles: [`
    .qts-skeleton {
      display: inline-block;
      background-color: #e5e7eb;
      position: relative;
      overflow: hidden;
    }
    
    /* Variants */
    .qts-skeleton--text {
      border-radius: 0.25rem;
      height: 1rem;
    }
    
    .qts-skeleton--circular {
      border-radius: 50%;
    }
    
    .qts-skeleton--rectangular {
      border-radius: 0;
    }
    
    .qts-skeleton--rounded {
      border-radius: 0.5rem;
    }
    
    /* Animations */
    .qts-skeleton--pulse {
      animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    .qts-skeleton--wave {
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.4), 
        transparent);
      background-size: 200% 100%;
      animation: skeleton-wave 1.5s ease-in-out infinite;
    }
    
    .skeleton-wave {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.6), 
        transparent);
      animation: skeleton-wave-move 1.5s ease-in-out infinite;
    }
    
    /* Keyframes */
    @keyframes skeleton-pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
    
    @keyframes skeleton-wave {
      0% {
        background-position: -200% 0;
      }
      100% {
        background-position: 200% 0;
      }
    }
    
    @keyframes skeleton-wave-move {
      0% {
        left: -100%;
      }
      100% {
        left: 100%;
      }
    }
    
    /* Dark Mode */
    @media (prefers-color-scheme: dark) {
      .qts-skeleton {
        background-color: #374151;
      }
      
      .qts-skeleton--wave {
        background: linear-gradient(90deg, 
          transparent, 
          rgba(255, 255, 255, 0.1), 
          transparent);
      }
      
      .skeleton-wave {
        background: linear-gradient(90deg, 
          transparent, 
          rgba(255, 255, 255, 0.2), 
          transparent);
      }
    }
    
    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
      .qts-skeleton--pulse,
      .qts-skeleton--wave,
      .skeleton-wave {
        animation: none;
      }
      
      .qts-skeleton {
        opacity: 0.7;
      }
    }
    
    /* High Contrast */
    @media (prefers-contrast: high) {
      .qts-skeleton {
        background-color: #6b7280;
        border: 1px solid #374151;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SkeletonLoaderComponent {
  @Input() variant: SkeletonVariant = 'rectangular';
  @Input() animation: SkeletonAnimation = 'pulse';
  @Input() width = '100%';
  @Input() height = '1rem';
  @Input() ariaLabel?: string;
  
  getSkeletonClasses(): string {
    const classes = [
      'qts-skeleton',
      `qts-skeleton--${this.variant}`
    ];
    
    if (this.animation !== 'none') {
      classes.push(`qts-skeleton--${this.animation}`);
    }
    
    return classes.join(' ');
  }
  
  getBorderRadius(): string {
    switch (this.variant) {
      case 'circular':
        return '50%';
      case 'rounded':
        return '0.5rem';
      case 'text':
        return '0.25rem';
      default:
        return '0';
    }
  }
}

// Skeleton Templates Component
@Component({
  selector: 'qts-skeleton-templates',
  standalone: true,
  imports: [CommonModule, SkeletonLoaderComponent],
  template: `
    <!-- Card Skeleton -->
    @if (template === 'card') {
      <div class="skeleton-card">
        <qts-skeleton-loader 
          variant="rectangular" 
          height="200px" 
          animation="wave">
        </qts-skeleton-loader>
        <div class="skeleton-card-content">
          <qts-skeleton-loader 
            variant="text" 
            height="1.5rem" 
            width="80%">
          </qts-skeleton-loader>
          <qts-skeleton-loader 
            variant="text" 
            height="1rem" 
            width="60%">
          </qts-skeleton-loader>
          <div class="skeleton-card-actions">
            <qts-skeleton-loader 
              variant="rounded" 
              height="2.5rem" 
              width="100px">
            </qts-skeleton-loader>
            <qts-skeleton-loader 
              variant="rounded" 
              height="2.5rem" 
              width="80px">
            </qts-skeleton-loader>
          </div>
        </div>
      </div>
    }
    
    <!-- Profile Skeleton -->
    @if (template === 'profile') {
      <div class="skeleton-profile">
        <qts-skeleton-loader 
          variant="circular" 
          width="80px" 
          height="80px">
        </qts-skeleton-loader>
        <div class="skeleton-profile-info">
          <qts-skeleton-loader 
            variant="text" 
            height="1.5rem" 
            width="150px">
          </qts-skeleton-loader>
          <qts-skeleton-loader 
            variant="text" 
            height="1rem" 
            width="100px">
          </qts-skeleton-loader>
        </div>
      </div>
    }
    
    <!-- List Skeleton -->
    @if (template === 'list') {
      <div class="skeleton-list">
        @for (item of getListItems(); track $index) {
          <div class="skeleton-list-item">
            <qts-skeleton-loader 
              variant="circular" 
              width="40px" 
              height="40px">
            </qts-skeleton-loader>
            <div class="skeleton-list-content">
              <qts-skeleton-loader 
                variant="text" 
                height="1rem" 
                width="70%">
              </qts-skeleton-loader>
              <qts-skeleton-loader 
                variant="text" 
                height="0.875rem" 
                width="50%">
              </qts-skeleton-loader>
            </div>
          </div>
        }
      </div>
    }
    
    <!-- Article Skeleton -->
    @if (template === 'article') {
      <div class="skeleton-article">
        <qts-skeleton-loader 
          variant="text" 
          height="2rem" 
          width="80%">
        </qts-skeleton-loader>
        <qts-skeleton-loader 
          variant="text" 
          height="1rem" 
          width="40%">
        </qts-skeleton-loader>
        <qts-skeleton-loader 
          variant="rectangular" 
          height="200px" 
          class="skeleton-article-image">
        </qts-skeleton-loader>
        @for (line of getArticleLines(); track $index) {
          <qts-skeleton-loader 
            variant="text" 
            height="1rem" 
            [width]="line.width">
          </qts-skeleton-loader>
        }
      </div>
    }
  `,
  styles: [`
    .skeleton-card {
      border: 1px solid #e5e7eb;
      border-radius: 0.75rem;
      overflow: hidden;
      background: white;
    }
    
    .skeleton-card-content {
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .skeleton-card-actions {
      display: flex;
      gap: 0.75rem;
      margin-top: 0.5rem;
    }
    
    .skeleton-profile {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
    }
    
    .skeleton-profile-info {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .skeleton-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .skeleton-list-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
    }
    
    .skeleton-list-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .skeleton-article {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 1.5rem;
    }
    
    .skeleton-article-image {
      margin: 1rem 0;
    }
    
    /* Dark Mode */
    @media (prefers-color-scheme: dark) {
      .skeleton-card,
      .skeleton-list-item {
        background: #1f2937;
        border-color: #374151;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SkeletonTemplatesComponent {
  @Input() template: 'card' | 'profile' | 'list' | 'article' = 'card';
  @Input() listItems = 3;
  
  getListItems(): number[] {
    return Array(this.listItems).fill(0);
  }
  
  getArticleLines(): Array<{width: string}> {
    return [
      { width: '100%' },
      { width: '95%' },
      { width: '88%' },
      { width: '92%' },
      { width: '85%' },
      { width: '90%' },
      { width: '75%' }
    ];
  }
}
