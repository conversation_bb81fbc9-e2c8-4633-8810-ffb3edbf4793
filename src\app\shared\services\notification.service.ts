import { Injectable, ComponentRef, ViewContainerRef, inject } from '@angular/core';
import { BehaviorSubject, Observable, timer } from 'rxjs';
import { take } from 'rxjs/operators';

export interface NotificationConfig {
  id?: string;
  title?: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
  icon?: string;
  position?: NotificationPosition;
  showProgress?: boolean;
  allowDismiss?: boolean;
  progress?: number;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary';
}

export type NotificationPosition =
  | 'top-right'
  | 'top-left'
  | 'top-center'
  | 'bottom-right'
  | 'bottom-left'
  | 'bottom-center';

export interface ActiveNotification extends NotificationConfig {
  id: string;
  timestamp: number;
  progress?: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notifications$ = new BehaviorSubject<ActiveNotification[]>([]);
  private viewContainerRef?: ViewContainerRef;
  private defaultDuration = 5000;
  private maxNotifications = 5;

  constructor() {}

  setViewContainerRef(vcr: ViewContainerRef): void {
    this.viewContainerRef = vcr;
  }

  getNotifications(): Observable<ActiveNotification[]> {
    return this.notifications$.asObservable();
  }

  show(config: NotificationConfig): string {
    const notification: ActiveNotification = {
      ...config,
      id: config.id || this.generateId(),
      timestamp: Date.now(),
      duration: config.duration ?? this.defaultDuration,
      persistent: config.persistent ?? false,
      allowDismiss: config.allowDismiss ?? true,
      position: config.position ?? 'top-right',
      showProgress: config.showProgress ?? true
    };

    this.addNotification(notification);

    // Auto-dismiss if not persistent
    if (!notification.persistent && notification.duration! > 0) {
      this.scheduleAutoDismiss(notification);
    }

    return notification.id;
  }

  success(message: string, title?: string, options?: Partial<NotificationConfig>): string {
    return this.show({
      ...options,
      message,
      title,
      type: 'success',
      icon: options?.icon || 'check_circle'
    });
  }

  error(message: string, title?: string, options?: Partial<NotificationConfig>): string {
    return this.show({
      ...options,
      message,
      title,
      type: 'error',
      icon: options?.icon || 'error',
      persistent: options?.persistent ?? true // Errors are persistent by default
    });
  }

  warning(message: string, title?: string, options?: Partial<NotificationConfig>): string {
    return this.show({
      ...options,
      message,
      title,
      type: 'warning',
      icon: options?.icon || 'warning'
    });
  }

  info(message: string, title?: string, options?: Partial<NotificationConfig>): string {
    return this.show({
      ...options,
      message,
      title,
      type: 'info',
      icon: options?.icon || 'info'
    });
  }

  dismiss(id: string): void {
    const notifications = this.notifications$.value;
    const updatedNotifications = notifications.filter(n => n.id !== id);
    this.notifications$.next(updatedNotifications);
  }

  dismissAll(): void {
    this.notifications$.next([]);
  }

  update(id: string, updates: Partial<NotificationConfig>): void {
    const notifications = this.notifications$.value;
    const index = notifications.findIndex(n => n.id === id);

    if (index !== -1) {
      const updatedNotification = { ...notifications[index], ...updates };
      const updatedNotifications = [...notifications];
      updatedNotifications[index] = updatedNotification;
      this.notifications$.next(updatedNotifications);
    }
  }

  // Convenience methods for common scenarios
  showSaveSuccess(): string {
    return this.success('Changes saved successfully', 'Success');
  }

  showSaveError(error?: string): string {
    return this.error(
      error || 'Failed to save changes. Please try again.',
      'Save Failed'
    );
  }

  showLoadingError(): string {
    return this.error(
      'Failed to load data. Please refresh the page.',
      'Loading Error'
    );
  }

  showNetworkError(): string {
    return this.error(
      'Network connection lost. Please check your internet connection.',
      'Connection Error',
      {
        actions: [
          {
            label: 'Retry',
            action: () => window.location.reload(),
            style: 'primary'
          }
        ]
      }
    );
  }

  showValidationError(message: string): string {
    return this.warning(message, 'Validation Error');
  }

  showConfirmation(
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ): string {
    return this.info(message, 'Confirmation Required', {
      persistent: true,
      actions: [
        {
          label: 'Confirm',
          action: () => {
            onConfirm();
            // The notification will be dismissed automatically
          },
          style: 'primary'
        },
        {
          label: 'Cancel',
          action: () => {
            if (onCancel) onCancel();
            // The notification will be dismissed automatically
          },
          style: 'secondary'
        }
      ]
    });
  }

  private addNotification(notification: ActiveNotification): void {
    const notifications = this.notifications$.value;

    // Remove oldest notification if we've reached the limit
    if (notifications.length >= this.maxNotifications) {
      notifications.shift();
    }

    this.notifications$.next([...notifications, notification]);
  }

  private scheduleAutoDismiss(notification: ActiveNotification): void {
    if (!notification.duration || notification.duration <= 0) return;

    // Update progress if enabled
    if (notification.showProgress) {
      this.updateProgress(notification);
    }

    timer(notification.duration).pipe(take(1)).subscribe(() => {
      this.dismiss(notification.id);
    });
  }

  private updateProgress(notification: ActiveNotification): void {
    if (!notification.duration || !notification.showProgress) return;

    const interval = 100; // Update every 100ms
    const steps = notification.duration / interval;
    let currentStep = 0;

    const progressTimer = setInterval(() => {
      currentStep++;
      const progress = (currentStep / steps) * 100;

      this.update(notification.id, { progress });

      if (currentStep >= steps) {
        clearInterval(progressTimer);
      }
    }, interval);
  }

  private generateId(): string {
    return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Notification Component Interface
export interface NotificationComponentData {
  notification: ActiveNotification;
  onDismiss: (id: string) => void;
  onAction: (action: NotificationAction, id: string) => void;
}

// Notification Container Component
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Subscription } from 'rxjs';

@Component({
  selector: 'qts-notification-container',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div class="notification-container">
      @for (position of positions; track position) {
        <div [class]="'notification-stack notification-stack--' + position">
          @for (notification of getNotificationsForPosition(position); track notification.id) {
            <div
              [class]="getNotificationClasses(notification)"
              [attr.role]="'alert'"
              [attr.aria-live]="notification.type === 'error' ? 'assertive' : 'polite'">

              <!-- Progress Bar -->
              @if (notification.showProgress && notification.progress !== undefined) {
                <div class="notification-progress">
                  <div
                    class="notification-progress-bar"
                    [style.width]="notification.progress + '%'">
                  </div>
                </div>
              }

              <!-- Icon -->
              @if (notification.icon) {
                <div class="notification-icon">
                  <mat-icon>{{ notification.icon }}</mat-icon>
                </div>
              }

              <!-- Content -->
              <div class="notification-content">
                @if (notification.title) {
                  <div class="notification-title">{{ notification.title }}</div>
                }
                <div class="notification-message">{{ notification.message }}</div>

                <!-- Actions -->
                @if (notification.actions && notification.actions.length > 0) {
                  <div class="notification-actions">
                    @for (action of notification.actions; track action.label) {
                      <button
                        mat-button
                        [class]="'notification-action notification-action--' + (action.style || 'secondary')"
                        (click)="handleAction(action, notification.id)">
                        {{ action.label }}
                      </button>
                    }
                  </div>
                }
              </div>

              <!-- Dismiss Button -->
              @if (notification.allowDismiss) {
                <button
                  mat-icon-button
                  class="notification-dismiss"
                  (click)="dismiss(notification.id)"
                  [attr.aria-label]="'Dismiss notification'">
                  <mat-icon>close</mat-icon>
                </button>
              }
            </div>
          }
        </div>
      }
    </div>
  `,
  styles: [`
    .notification-container {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 1700;
    }

    .notification-stack {
      position: absolute;
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      padding: 1rem;
      max-width: 400px;
      width: 100%;
    }

    .notification-stack--top-right {
      top: 0;
      right: 0;
    }

    .notification-stack--top-left {
      top: 0;
      left: 0;
    }

    .notification-stack--top-center {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    .notification-stack--bottom-right {
      bottom: 0;
      right: 0;
      flex-direction: column-reverse;
    }

    .notification-stack--bottom-left {
      bottom: 0;
      left: 0;
      flex-direction: column-reverse;
    }

    .notification-stack--bottom-center {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      flex-direction: column-reverse;
    }

    .notification {
      position: relative;
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      padding: 1rem;
      border-radius: 0.75rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      pointer-events: auto;
      animation: slideIn 0.3s ease-out;
      overflow: hidden;
    }

    .notification--success {
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.95) 0%, rgba(5, 150, 105, 0.95) 100%);
      color: white;
    }

    .notification--error {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 0.95) 100%);
      color: white;
    }

    .notification--warning {
      background: linear-gradient(135deg, rgba(245, 158, 11, 0.95) 0%, rgba(217, 119, 6, 0.95) 100%);
      color: white;
    }

    .notification--info {
      background: linear-gradient(135deg, rgba(6, 182, 212, 0.95) 0%, rgba(8, 145, 178, 0.95) 100%);
      color: white;
    }

    .notification-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: rgba(255, 255, 255, 0.2);
    }

    .notification-progress-bar {
      height: 100%;
      background: rgba(255, 255, 255, 0.8);
      transition: width 0.1s linear;
    }

    .notification-icon {
      flex-shrink: 0;
      margin-top: 0.125rem;
    }

    .notification-icon mat-icon {
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
    }

    .notification-content {
      flex: 1;
      min-width: 0;
    }

    .notification-title {
      font-weight: 600;
      font-size: 0.875rem;
      margin-bottom: 0.25rem;
    }

    .notification-message {
      font-size: 0.875rem;
      line-height: 1.4;
      opacity: 0.95;
    }

    .notification-actions {
      display: flex;
      gap: 0.5rem;
      margin-top: 0.75rem;
    }

    .notification-action {
      font-size: 0.75rem !important;
      padding: 0.25rem 0.75rem !important;
      min-width: auto !important;
      height: auto !important;
      border-radius: 0.375rem !important;
    }

    .notification-action--primary {
      background: rgba(255, 255, 255, 0.2) !important;
      color: white !important;
    }

    .notification-action--secondary {
      background: transparent !important;
      color: rgba(255, 255, 255, 0.8) !important;
      border: 1px solid rgba(255, 255, 255, 0.3) !important;
    }

    .notification-dismiss {
      flex-shrink: 0;
      width: 2rem !important;
      height: 2rem !important;
      margin-top: -0.25rem;
      margin-right: -0.25rem;
      color: rgba(255, 255, 255, 0.8) !important;
    }

    .notification-dismiss mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateX(100%);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    /* Mobile Responsive */
    @media (max-width: 640px) {
      .notification-stack {
        left: 0.5rem;
        right: 0.5rem;
        max-width: none;
        padding: 0.5rem;
      }

      .notification-stack--top-center,
      .notification-stack--bottom-center {
        left: 0.5rem;
        right: 0.5rem;
        transform: none;
      }
    }

    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
      .notification {
        animation: none;
      }

      .notification-progress-bar {
        transition: none;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NotificationContainerComponent implements OnInit, OnDestroy {
  notifications: ActiveNotification[] = [];
  positions: NotificationPosition[] = [
    'top-right', 'top-left', 'top-center',
    'bottom-right', 'bottom-left', 'bottom-center'
  ];

  private subscription = new Subscription();

  constructor(private notificationService: NotificationService) {}

  ngOnInit() {
    this.subscription.add(
      this.notificationService.getNotifications().subscribe(
        notifications => this.notifications = notifications
      )
    );
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  getNotificationsForPosition(position: NotificationPosition): ActiveNotification[] {
    return this.notifications.filter(n => n.position === position);
  }

  getNotificationClasses(notification: ActiveNotification): string {
    return `notification notification--${notification.type}`;
  }

  dismiss(id: string): void {
    this.notificationService.dismiss(id);
  }

  handleAction(action: NotificationAction, notificationId: string): void {
    action.action();
    this.dismiss(notificationId);
  }
}
