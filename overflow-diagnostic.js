// Horizontal Overflow Diagnostic Script
// Run this in the browser console to check for overflow issues

console.log('🔍 Running Horizontal Overflow Diagnostic...\n');

// Check viewport and document dimensions
function checkViewportDimensions() {
  console.log('📏 Viewport & Document Dimensions:');
  console.log(`Viewport width: ${window.innerWidth}px`);
  console.log(`Document width: ${document.documentElement.scrollWidth}px`);
  console.log(`Body width: ${document.body.scrollWidth}px`);
  console.log(`Overflow detected: ${document.documentElement.scrollWidth > window.innerWidth ? '❌ YES' : '✅ NO'}\n`);
}

// Find elements that are wider than the viewport
function findOverflowingElements() {
  console.log('🔍 Checking for overflowing elements...');
  const elements = document.querySelectorAll('*');
  const overflowing = [];
  
  elements.forEach(el => {
    const rect = el.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(el);
    
    // Check if element extends beyond viewport
    if (rect.right > window.innerWidth || el.scrollWidth > window.innerWidth) {
      overflowing.push({
        element: el,
        tagName: el.tagName,
        className: el.className,
        id: el.id,
        scrollWidth: el.scrollWidth,
        clientWidth: el.clientWidth,
        offsetWidth: el.offsetWidth,
        rightEdge: rect.right,
        boxSizing: computedStyle.boxSizing,
        overflow: computedStyle.overflow,
        overflowX: computedStyle.overflowX
      });
      
      // Highlight the element
      el.style.outline = '3px solid red';
      el.style.outlineOffset = '2px';
    }
  });
  
  if (overflowing.length > 0) {
    console.log(`❌ Found ${overflowing.length} overflowing elements:`);
    console.table(overflowing);
  } else {
    console.log('✅ No overflowing elements found!');
  }
  
  return overflowing;
}

// Check specific elements that commonly cause overflow
function checkCommonCulprits() {
  console.log('🎯 Checking common overflow culprits...');
  
  const culprits = [
    'body',
    'html',
    '.hero-section',
    '.hero-container',
    '.container',
    '.navbar',
    '.footer',
    '.main-content',
    'router-outlet'
  ];
  
  culprits.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => {
      const rect = el.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(el);
      
      console.log(`${selector}:`, {
        width: `${el.offsetWidth}px`,
        scrollWidth: `${el.scrollWidth}px`,
        rightEdge: `${rect.right}px`,
        boxSizing: computedStyle.boxSizing,
        overflowX: computedStyle.overflowX,
        maxWidth: computedStyle.maxWidth,
        isOverflowing: rect.right > window.innerWidth ? '❌' : '✅'
      });
    });
  });
}

// Remove highlighting
function clearHighlights() {
  document.querySelectorAll('*').forEach(el => {
    el.style.outline = '';
    el.style.outlineOffset = '';
  });
  console.log('🧹 Cleared all highlights');
}

// Main diagnostic function
function runDiagnostic() {
  console.clear();
  console.log('🔍 Horizontal Overflow Diagnostic Tool\n');
  
  checkViewportDimensions();
  const overflowing = findOverflowingElements();
  checkCommonCulprits();
  
  console.log('\n📋 Summary:');
  if (overflowing.length === 0) {
    console.log('✅ No horizontal overflow detected!');
    console.log('✅ The black space issue should be resolved.');
  } else {
    console.log(`❌ Found ${overflowing.length} elements causing overflow.`);
    console.log('🔧 Check the highlighted elements and their CSS properties.');
  }
  
  console.log('\n🛠️ Available commands:');
  console.log('- runDiagnostic() - Run full diagnostic');
  console.log('- clearHighlights() - Remove red outlines');
  console.log('- checkViewportDimensions() - Check dimensions only');
  console.log('- findOverflowingElements() - Find overflowing elements only');
}

// Make functions available globally
window.runDiagnostic = runDiagnostic;
window.clearHighlights = clearHighlights;
window.checkViewportDimensions = checkViewportDimensions;
window.findOverflowingElements = findOverflowingElements;

// Auto-run diagnostic
runDiagnostic();
