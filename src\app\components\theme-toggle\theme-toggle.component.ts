import { Component, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ThemeService, Theme } from '../../services/theme.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule, MatTooltipModule],
  template: `
    <button 
      mat-icon-button 
      class="theme-toggle"
      [class.dark-mode]="isDarkMode"
      (click)="toggleTheme()"
      [matTooltip]="getTooltipText()"
      matTooltipPosition="below">
      
      <div class="icon-container">
        @if (currentTheme === 'light') {
          <mat-icon class="theme-icon light-icon">light_mode</mat-icon>
        } @else if (currentTheme === 'dark') {
          <mat-icon class="theme-icon dark-icon">dark_mode</mat-icon>
        } @else {
          <mat-icon class="theme-icon auto-icon">brightness_auto</mat-icon>
        }
      </div>
    </button>
  `,
  styles: [`
    .theme-toggle {
      position: relative;
      width: 48px !important;
      height: 48px !important;
      border-radius: 50% !important;
      background: rgba(255, 255, 255, 0.1) !important;
      backdrop-filter: blur(10px) !important;
      border: 2px solid rgba(255, 255, 255, 0.2) !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      overflow: hidden;
    }
    
    .theme-toggle:hover {
      background: rgba(255, 255, 255, 0.2) !important;
      transform: scale(1.1);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .theme-toggle.dark-mode {
      background: rgba(0, 0, 0, 0.2) !important;
      border-color: rgba(255, 255, 255, 0.3) !important;
    }
    
    .theme-toggle.dark-mode:hover {
      background: rgba(0, 0, 0, 0.3) !important;
    }
    
    .icon-container {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .theme-icon {
      font-size: 24px !important;
      width: 24px !important;
      height: 24px !important;
      transition: all 0.3s ease !important;
      color: white !important;
    }
    
    .light-icon {
      animation: sunRotate 8s linear infinite;
    }
    
    .dark-icon {
      animation: moonGlow 2s ease-in-out infinite alternate;
    }
    
    .auto-icon {
      animation: autoFlicker 3s ease-in-out infinite;
    }
    
    .theme-toggle:hover .theme-icon {
      transform: scale(1.2);
    }
    
    /* Light mode icon animation */
    @keyframes sunRotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    
    /* Dark mode icon animation */
    @keyframes moonGlow {
      0% { 
        filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
        transform: scale(1);
      }
      100% { 
        filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
        transform: scale(1.05);
      }
    }
    
    /* Auto mode icon animation */
    @keyframes autoFlicker {
      0%, 100% { opacity: 1; }
      25% { opacity: 0.7; }
      50% { opacity: 1; }
      75% { opacity: 0.8; }
    }
    
    /* Ripple effect on click */
    .theme-toggle::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      transition: width 0.3s ease, height 0.3s ease;
      pointer-events: none;
    }
    
    .theme-toggle:active::after {
      width: 100px;
      height: 100px;
    }
    
    /* Accessibility improvements */
    .theme-toggle:focus {
      outline: 2px solid #0607E1;
      outline-offset: 2px;
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      .theme-toggle,
      .theme-icon,
      .theme-toggle::after {
        animation: none !important;
        transition: none !important;
      }
    }
    
    /* High contrast mode support */
    @media (prefers-contrast: high) {
      .theme-toggle {
        border-width: 3px !important;
        background: rgba(0, 0, 0, 0.8) !important;
      }
      
      .theme-icon {
        color: white !important;
        filter: contrast(2);
      }
    }
  `]
})
export class ThemeToggleComponent implements OnInit, OnDestroy {
  currentTheme: Theme = 'light';
  isDarkMode = false;
  private subscriptions = new Subscription();
  
  constructor(private themeService: ThemeService) {}
  
  ngOnInit() {
    // Subscribe to theme changes
    this.subscriptions.add(
      this.themeService.theme$.subscribe(theme => {
        this.currentTheme = theme;
      })
    );
    
    this.subscriptions.add(
      this.themeService.isDarkMode$.subscribe(isDark => {
        this.isDarkMode = isDark;
      })
    );
  }
  
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
  
  toggleTheme() {
    this.themeService.toggleTheme();
  }
  
  getTooltipText(): string {
    switch (this.currentTheme) {
      case 'light':
        return 'Switch to Dark Mode';
      case 'dark':
        return 'Switch to Auto Mode';
      case 'auto':
        return 'Switch to Light Mode';
      default:
        return 'Toggle Theme';
    }
  }
}
