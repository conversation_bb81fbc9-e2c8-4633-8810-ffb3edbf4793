# Firebase SSR Deployment Fix Guide

## Issue Description

When deploying an Angular application with Server-Side Rendering (SSR) to Firebase Hosting, you may encounter a "Page Not Found" error. This occurs because Angular SSR generates `index.csr.html` (Client-Side Rendering) instead of the expected `index.html` file that Firebase Hosting looks for.

## Root Cause

- **Angular SSR Build Output**: Generates `index.csr.html` for client-side rendering
- **Firebase Hosting Expectation**: Looks for `index.html` as the main entry point
- **Result**: Firebase cannot find the main HTML file, leading to 404 errors

## Solutions

### Solution 1: Automated File Copy in Deploy Script (Recommended)

Update your deployment script to automatically copy the CSR file to the expected filename:

#### Update package.json

```json
{
  "scripts": {
    "deploy": "ng build --configuration production && copy \"dist\\qts-angular\\browser\\index.csr.html\" \"dist\\qts-angular\\browser\\index.html\" && firebase deploy",
    "deploy:windows": "ng build --configuration production && copy \"dist\\qts-angular\\browser\\index.csr.html\" \"dist\\qts-angular\\browser\\index.html\" && firebase deploy",
    "deploy:unix": "ng build --configuration production && cp dist/qts-angular/browser/index.csr.html dist/qts-angular/browser/index.html && firebase deploy"
  }
}
```

#### Usage

```bash
# For Windows
npm run deploy
# or
npm run deploy:windows

# For Unix/Linux/macOS
npm run deploy:unix
```

### Solution 2: Modify Firebase Configuration

Alternatively, update your `firebase.json` to use `index.csr.html` as the fallback:

```json
{
  "hosting": {
    "public": "dist/qts-angular/browser",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.csr.html"
      }
    ]
  }
}
```

### Solution 3: Manual Fix (One-time)

For immediate resolution:

```bash
# Windows
copy "dist\\qts-angular\\browser\\index.csr.html" "dist\\qts-angular\\browser\\index.html"
firebase deploy

# Unix/Linux/macOS
cp dist/qts-angular/browser/index.csr.html dist/qts-angular/browser/index.html
firebase deploy
```

## Recommended Approach

**Use Solution 1** (Automated File Copy) because:

✅ **Maintains compatibility** with Firebase's expected file structure
✅ **Preserves Angular SSR functionality** 
✅ **Automated process** - no manual intervention required
✅ **Works across different environments** with platform-specific scripts
✅ **Future-proof** - works with Angular updates

## Verification Steps

1. **Build the project**:
   ```bash
   ng build --configuration production
   ```

2. **Check build output**:
   ```bash
   ls dist/qts-angular/browser/
   # Should see both index.csr.html and index.html
   ```

3. **Deploy to Firebase**:
   ```bash
   firebase deploy
   ```

4. **Test the application**:
   - Visit your Firebase hosting URL
   - Verify the application loads correctly
   - Test navigation between routes

## Troubleshooting

### Issue: Copy command fails
**Solution**: Ensure the build completed successfully and the source file exists

### Issue: Firebase still shows 404
**Solutions**:
1. Clear browser cache
2. Wait a few minutes for Firebase CDN to update
3. Check Firebase console for deployment status

### Issue: Application loads but routing doesn't work
**Solution**: Verify the `rewrites` configuration in `firebase.json` is correct

## Additional Notes

- This issue is specific to Angular applications with SSR enabled
- The fix maintains both SSR and CSR functionality
- Consider this when setting up CI/CD pipelines
- Test thoroughly after implementing the fix

## Related Files

- `firebase.json` - Firebase hosting configuration
- `package.json` - NPM scripts for deployment
- `angular.json` - Angular build configuration
- `dist/qts-angular/browser/` - Build output directory

---

**Last Updated**: December 2024  
**Angular Version**: 20.x  
**Firebase CLI Version**: 14.x