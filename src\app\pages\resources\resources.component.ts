import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { RouterLink } from '@angular/router';
import { SEOService } from '../../services/seo.service';

interface Resource {
  title: string;
  description: string;
  type: 'whitepaper' | 'case-study' | 'guide' | 'webinar' | 'documentation';
  category: string;
  downloadUrl?: string;
  readTime?: string;
  publishDate: string;
  tags: string[];
}

@Component({
  selector: 'app-resources',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatTabsModule, MatChipsModule, RouterLink],
  template: `
    <div class="resources-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Knowledge Hub & Resources</h1>
          <p class="hero-subtitle">
            Access our comprehensive library of whitepapers, case studies, guides, and documentation 
            to accelerate your digital transformation journey.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">200+</span>
              <span class="stat-label">Resources Available</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">50K+</span>
              <span class="stat-label">Downloads</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">Weekly</span>
              <span class="stat-label">New Content</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Resources Content -->
      <div class="resources-section">
        <div class="container">
          <mat-tab-group class="resource-tabs">
            <mat-tab label="Whitepapers">
              <div class="resources-grid">
                @for (resource of whitepapers; track resource.title) {
                  <mat-card class="resource-card">
                    <mat-card-header>
                      <mat-icon class="resource-icon whitepaper">description</mat-icon>
                      <mat-card-title>{{ resource.title }}</mat-card-title>
                      <mat-card-subtitle>{{ resource.publishDate }} • {{ resource.readTime }}</mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ resource.description }}</p>
                      <div class="tags">
                        @for (tag of resource.tags; track tag) {
                          <mat-chip>{{ tag }}</mat-chip>
                        }
                      </div>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">Download PDF</button>
                      <button mat-stroked-button>Preview</button>
                    </mat-card-actions>
                  </mat-card>
                }
              </div>
            </mat-tab>

            <mat-tab label="Case Studies">
              <div class="resources-grid">
                @for (resource of caseStudies; track resource.title) {
                  <mat-card class="resource-card">
                    <mat-card-header>
                      <mat-icon class="resource-icon case-study">business_center</mat-icon>
                      <mat-card-title>{{ resource.title }}</mat-card-title>
                      <mat-card-subtitle>{{ resource.publishDate }} • {{ resource.readTime }}</mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ resource.description }}</p>
                      <div class="tags">
                        @for (tag of resource.tags; track tag) {
                          <mat-chip>{{ tag }}</mat-chip>
                        }
                      </div>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary" routerLink="/case-studies">Read Full Story</button>
                      <button mat-stroked-button>Download Summary</button>
                    </mat-card-actions>
                  </mat-card>
                }
              </div>
            </mat-tab>

            <mat-tab label="Technical Guides">
              <div class="resources-grid">
                @for (resource of guides; track resource.title) {
                  <mat-card class="resource-card">
                    <mat-card-header>
                      <mat-icon class="resource-icon guide">menu_book</mat-icon>
                      <mat-card-title>{{ resource.title }}</mat-card-title>
                      <mat-card-subtitle>{{ resource.publishDate }} • {{ resource.readTime }}</mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ resource.description }}</p>
                      <div class="tags">
                        @for (tag of resource.tags; track tag) {
                          <mat-chip>{{ tag }}</mat-chip>
                        }
                      </div>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">Access Guide</button>
                      <button mat-stroked-button>Download</button>
                    </mat-card-actions>
                  </mat-card>
                }
              </div>
            </mat-tab>

            <mat-tab label="Documentation">
              <div class="documentation-section">
                <div class="doc-categories">
                  <mat-card class="doc-category">
                    <mat-card-header>
                      <mat-icon class="doc-icon">code</mat-icon>
                      <mat-card-title>API Documentation</mat-card-title>
                    </mat-card-header>
                    <mat-card-content>
                      <p>Comprehensive API references and integration guides for all our products.</p>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">View APIs</button>
                    </mat-card-actions>
                  </mat-card>

                  <mat-card class="doc-category">
                    <mat-card-header>
                      <mat-icon class="doc-icon">settings</mat-icon>
                      <mat-card-title>Implementation Guides</mat-card-title>
                    </mat-card-header>
                    <mat-card-content>
                      <p>Step-by-step implementation guides for seamless deployment and configuration.</p>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">Get Started</button>
                    </mat-card-actions>
                  </mat-card>

                  <mat-card class="doc-category">
                    <mat-card-header>
                      <mat-icon class="doc-icon">support</mat-icon>
                      <mat-card-title>Support Resources</mat-card-title>
                    </mat-card-header>
                    <mat-card-content>
                      <p>Troubleshooting guides, FAQs, and best practices for optimal performance.</p>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-raised-button color="primary">Browse Support</button>
                    </mat-card-actions>
                  </mat-card>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>

      <!-- Newsletter Section -->
      <div class="newsletter-section">
        <div class="container">
          <h2>Stay Updated</h2>
          <p>Subscribe to our newsletter for the latest insights, resources, and industry trends.</p>
          <div class="newsletter-form">
            <button mat-raised-button color="primary">Subscribe to Newsletter</button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .resources-page {
      min-height: 100vh;
    }

    .hero-section {
      background: linear-gradient(135deg, var(--generative-purple) 0%, var(--ai-blue) 100%);
      color: white;
      padding: 120px 0 80px;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      line-height: 1.2;
    }

    .hero-subtitle {
      font-size: 1.25rem;
      margin-bottom: 3rem;
      opacity: 0.9;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: 4rem;
      margin-top: 3rem;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .resources-section {
      padding: 80px 0;
    }

    .resource-tabs {
      margin-bottom: 2rem;
    }

    .resources-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .resource-card {
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .resource-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .resource-icon {
      font-size: 2rem;
      margin-right: 1rem;
    }

    .resource-icon.whitepaper { color: var(--ai-blue); }
    .resource-icon.case-study { color: var(--nlp-green); }
    .resource-icon.guide { color: var(--dev-orange); }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .documentation-section {
      margin-top: 2rem;
    }

    .doc-categories {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .doc-category {
      text-align: center;
      transition: transform 0.3s ease;
    }

    .doc-category:hover {
      transform: translateY(-5px);
    }

    .doc-icon {
      font-size: 3rem;
      color: var(--primary-blue);
      margin-bottom: 1rem;
    }

    .newsletter-section {
      background: #f8f9fa;
      padding: 80px 0;
      text-align: center;
    }

    .newsletter-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--primary-black);
    }

    .newsletter-section p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      color: #666;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2.5rem;
      }

      .hero-stats {
        flex-direction: column;
        gap: 2rem;
      }

      .resources-grid {
        grid-template-columns: 1fr;
      }

      .doc-categories {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ResourcesComponent implements OnInit {
  whitepapers: Resource[] = [
    {
      title: 'The Future of Enterprise AI: Strategic Implementation Guide',
      description: 'Comprehensive analysis of AI adoption strategies, implementation frameworks, and ROI optimization for enterprise organizations.',
      type: 'whitepaper',
      category: 'AI & Machine Learning',
      readTime: '15 min read',
      publishDate: 'Dec 2024',
      tags: ['AI Strategy', 'Enterprise', 'Digital Transformation']
    },
    {
      title: 'Cloud Migration Best Practices for Enterprise Applications',
      description: 'Essential strategies and methodologies for successful cloud migration, including risk mitigation and performance optimization.',
      type: 'whitepaper',
      category: 'Cloud Computing',
      readTime: '12 min read',
      publishDate: 'Nov 2024',
      tags: ['Cloud Migration', 'Enterprise Architecture', 'DevOps']
    }
  ];

  caseStudies: Resource[] = [
    {
      title: 'Global Bank Transforms Customer Experience with AI',
      description: 'How a leading financial institution increased customer satisfaction by 40% through AI-powered personalization and automation.',
      type: 'case-study',
      category: 'Financial Services',
      readTime: '8 min read',
      publishDate: 'Dec 2024',
      tags: ['Banking', 'AI', 'Customer Experience']
    },
    {
      title: 'Manufacturing Giant Achieves 35% Efficiency Gain',
      description: 'Complete digital transformation of manufacturing operations resulting in significant cost savings and productivity improvements.',
      type: 'case-study',
      category: 'Manufacturing',
      readTime: '10 min read',
      publishDate: 'Nov 2024',
      tags: ['Manufacturing', 'IoT', 'Industry 4.0']
    }
  ];

  guides: Resource[] = [
    {
      title: 'Enterprise Software Architecture Design Patterns',
      description: 'Comprehensive guide to modern software architecture patterns for scalable, maintainable enterprise applications.',
      type: 'guide',
      category: 'Software Development',
      readTime: '25 min read',
      publishDate: 'Dec 2024',
      tags: ['Architecture', 'Design Patterns', 'Best Practices']
    },
    {
      title: 'API Security Implementation Handbook',
      description: 'Complete guide to implementing robust API security measures, authentication protocols, and threat protection.',
      type: 'guide',
      category: 'Security',
      readTime: '20 min read',
      publishDate: 'Nov 2024',
      tags: ['API Security', 'Authentication', 'Cybersecurity']
    }
  ];

  constructor(private seoService: SEOService) {}

  ngOnInit() {
    this.seoService.updateSEO({
      title: 'Resources & Knowledge Hub - Quadrate Technologies',
      description: 'Access whitepapers, case studies, technical guides, and documentation to accelerate your digital transformation journey.',
      keywords: 'technology resources, whitepapers, case studies, technical guides, enterprise documentation, digital transformation'
    });
  }
}
