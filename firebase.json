{"hosting": {"public": "dist/qts-angular/browser", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(eot|otf|ttf|ttc|woff|font.css)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}}