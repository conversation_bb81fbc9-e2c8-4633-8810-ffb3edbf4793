import { Component, Input, HostListener, ElementRef, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

export type CardVariant = 'default' | 'elevated' | 'outlined' | 'filled' | 'glass';
export type CardSize = 'sm' | 'md' | 'lg' | 'xl';

@Component({
  selector: 'qts-advanced-card',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      [class]="getCardClasses()"
      [style.transform]="getTransform()"
      [style.box-shadow]="getShadow()"
      #cardElement>
      
      <!-- Background Gradient Overlay -->
      @if (variant === 'glass' || hasGradient) {
        <div class="card-gradient-overlay"></div>
      }
      
      <!-- Animated Background Particles -->
      @if (showParticles) {
        <div class="card-particles">
          @for (particle of particles; track particle.id) {
            <div 
              class="particle"
              [style.left]="particle.x + '%'"
              [style.top]="particle.y + '%'"
              [style.animation-delay]="particle.delay + 's'">
            </div>
          }
        </div>
      }
      
      <!-- Card Header -->
      @if (hasHeader) {
        <div class="card-header">
          <ng-content select="[slot=header]"></ng-content>
        </div>
      }
      
      <!-- Card Content -->
      <div class="card-content">
        <ng-content></ng-content>
      </div>
      
      <!-- Card Footer -->
      @if (hasFooter) {
        <div class="card-footer">
          <ng-content select="[slot=footer]"></ng-content>
        </div>
      }
      
      <!-- Hover Glow Effect -->
      @if (glowEffect) {
        <div class="card-glow" [class.active]="isHovered"></div>
      }
    </div>
  `,
  styles: [`
    :host {
      display: block;
      position: relative;
    }
    
    .qts-card {
      position: relative;
      border-radius: 1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      background: white;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    /* Variants */
    .qts-card--default {
      background: white;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }
    
    .qts-card--elevated {
      background: white;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .qts-card--outlined {
      background: white;
      border: 2px solid #e5e7eb;
      box-shadow: none;
    }
    
    .qts-card--filled {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: none;
      box-shadow: inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
    }
    
    .qts-card--glass {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }
    
    /* Sizes */
    .qts-card--sm {
      padding: 1rem;
      border-radius: 0.5rem;
    }
    
    .qts-card--md {
      padding: 1.5rem;
      border-radius: 0.75rem;
    }
    
    .qts-card--lg {
      padding: 2rem;
      border-radius: 1rem;
    }
    
    .qts-card--xl {
      padding: 2.5rem;
      border-radius: 1.25rem;
    }
    
    /* Interactive States */
    .qts-card--interactive {
      cursor: pointer;
    }
    
    .qts-card--interactive:hover {
      transform: translateY(-4px) scale(1.02);
    }
    
    .qts-card--tiltable:hover {
      transform-style: preserve-3d;
    }
    
    /* Card Sections */
    .card-header {
      margin-bottom: 1rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .card-content {
      position: relative;
      z-index: 2;
    }
    
    .card-footer {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    /* Gradient Overlay */
    .card-gradient-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, 
        rgba(6, 7, 225, 0.05) 0%, 
        rgba(77, 10, 255, 0.05) 50%, 
        rgba(6, 182, 212, 0.05) 100%);
      pointer-events: none;
      z-index: 1;
    }
    
    /* Particles */
    .card-particles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 1;
    }
    
    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: var(--vision-cyan);
      border-radius: 50%;
      opacity: 0.6;
      animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
      50% { transform: translateY(-10px) scale(1.2); opacity: 1; }
    }
    
    /* Glow Effect */
    .card-glow {
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, var(--primary-blue), var(--generative-purple), var(--vision-cyan));
      border-radius: inherit;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
      filter: blur(8px);
    }
    
    .card-glow.active {
      opacity: 0.3;
    }
    
    /* Dark Mode */
    @media (prefers-color-scheme: dark) {
      .qts-card--default,
      .qts-card--elevated {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
      }
      
      .qts-card--outlined {
        background: #1f2937;
        border-color: #4b5563;
        color: #f9fafb;
      }
      
      .qts-card--filled {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        color: #f9fafb;
      }
      
      .card-header,
      .card-footer {
        border-color: rgba(255, 255, 255, 0.1);
      }
    }
    
    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
      .qts-card {
        transition: none;
      }
      
      .qts-card--interactive:hover {
        transform: none;
      }
      
      .particle {
        animation: none;
      }
    }
    
    /* High Contrast */
    @media (prefers-contrast: high) {
      .qts-card {
        border-width: 2px;
        border-color: #000;
      }
      
      .qts-card--glass {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: none;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdvancedCardComponent implements OnInit, OnDestroy {
  @Input() variant: CardVariant = 'default';
  @Input() size: CardSize = 'md';
  @Input() interactive = false;
  @Input() tiltable = false;
  @Input() hasHeader = false;
  @Input() hasFooter = false;
  @Input() hasGradient = false;
  @Input() showParticles = false;
  @Input() glowEffect = false;
  
  isHovered = false;
  mouseX = 0;
  mouseY = 0;
  particles: Array<{id: number, x: number, y: number, delay: number}> = [];
  
  constructor(private elementRef: ElementRef) {}
  
  ngOnInit() {
    if (this.showParticles) {
      this.generateParticles();
    }
  }
  
  ngOnDestroy() {
    // Cleanup if needed
  }
  
  @HostListener('mouseenter')
  onMouseEnter() {
    this.isHovered = true;
  }
  
  @HostListener('mouseleave')
  onMouseLeave() {
    this.isHovered = false;
  }
  
  @HostListener('mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    if (!this.tiltable) return;
    
    const rect = this.elementRef.nativeElement.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    this.mouseX = (event.clientX - centerX) / (rect.width / 2);
    this.mouseY = (event.clientY - centerY) / (rect.height / 2);
  }
  
  getCardClasses(): string {
    const classes = [
      'qts-card',
      `qts-card--${this.variant}`,
      `qts-card--${this.size}`
    ];
    
    if (this.interactive) {
      classes.push('qts-card--interactive');
    }
    
    if (this.tiltable) {
      classes.push('qts-card--tiltable');
    }
    
    return classes.join(' ');
  }
  
  getTransform(): string {
    if (!this.tiltable || !this.isHovered) {
      return '';
    }
    
    const tiltX = this.mouseY * 10; // Max 10 degrees
    const tiltY = this.mouseX * -10; // Max 10 degrees
    
    return `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg)`;
  }
  
  getShadow(): string {
    if (!this.interactive || !this.isHovered) {
      return '';
    }
    
    return '0 20px 40px rgba(0, 0, 0, 0.15)';
  }
  
  private generateParticles() {
    for (let i = 0; i < 8; i++) {
      this.particles.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 2
      });
    }
  }
}
